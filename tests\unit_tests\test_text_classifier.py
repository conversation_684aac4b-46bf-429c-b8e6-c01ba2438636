"""
Unit tests for TextClassifier and EnsembleClassifier
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import torch
from pathlib import Path

from app.core.text_classifier import TextClassifier
from app.core.ensemble_classifier import EnsembleClassifier


class TestTextClassifier:
    """Test cases for TextClassifier"""
    
    def test_init_with_valid_path(self):
        """Test TextClassifier initialization with valid path"""
        classifier = TextClassifier("test/model/path")
        assert classifier.model_path == Path("test/model/path")
        assert classifier.labels == ["Non-Relevant", "Relevant"]
        assert classifier.threshold == 0.80
    
    def test_init_with_empty_path(self):
        """Test TextClassifier initialization with empty path raises error"""
        with pytest.raises(ValueError, match="TextClassifier must be initialized with a model_path"):
            TextClassifier("")
    
    @patch('app.core.text_classifier.AutoModelForSequenceClassification.from_pretrained')
    @patch('app.core.text_classifier.AutoTokenizer.from_pretrained')
    def test_load_model_success(self, mock_tokenizer, mock_model):
        """Test successful model loading"""
        # Setup mocks
        mock_model.return_value = MagicMock()
        mock_tokenizer.return_value = MagicMock()
        
        classifier = TextClassifier("test/model/path")
        
        # Test async load_model
        import asyncio
        asyncio.run(classifier.load_model())
        
        assert classifier.model is not None
        assert classifier.tokenizer is not None
        mock_model.assert_called_once()
        mock_tokenizer.assert_called_once()
    
    def test_classify_text_empty_input(self):
        """Test classify_text with empty input"""
        classifier = TextClassifier("test/model/path")
        
        # Test empty string
        result = classifier.classify_text("")
        assert result == []
        
        # Test whitespace only
        result = classifier.classify_text("   \n\t  ")
        assert result == []
    
    @patch.object(TextClassifier, '_classify_lines')
    def test_classify_text_normal_mode(self, mock_classify_lines):
        """Test classify_text in normal mode"""
        mock_classify_lines.return_value = [
            {"text": "line1", "prediction": "Relevant", "confidence": 0.9}
        ]
        
        classifier = TextClassifier("test/model/path")
        result = classifier.classify_text("line1\nline2\n", context_aware=False)
        
        mock_classify_lines.assert_called_once()
        assert len(result) == 1
    
    @patch.object(TextClassifier, '_classify_with_context_window')
    def test_classify_text_context_aware_mode(self, mock_classify_context):
        """Test classify_text in context-aware mode"""
        mock_classify_context.return_value = [
            {"text": "line1", "prediction": "Relevant", "confidence": 0.9}
        ]
        
        classifier = TextClassifier("test/model/path")
        result = classifier.classify_text("line1\nline2\n", context_aware=True)
        
        mock_classify_context.assert_called_once()
        assert len(result) == 1
    
    def test_classify_standard_inputs_empty(self):
        """Test classify_standard_inputs with empty input"""
        classifier = TextClassifier("test/model/path")
        result = classifier.classify_standard_inputs([])
        assert result == []
    
    @patch.object(TextClassifier, '_classify_lines')
    def test_classify_standard_inputs_normal(self, mock_classify_lines):
        """Test classify_standard_inputs with normal input"""
        mock_classify_lines.return_value = [
            {"prediction": "Relevant", "confidence": 0.9}
        ]
        
        classifier = TextClassifier("test/model/path")
        message_blocks = [
            {"text": "test message", "timestamp": "2024-01-01"},
            {"text": "another message", "timestamp": "2024-01-02"}
        ]
        
        result = classifier.classify_standard_inputs(message_blocks)
        
        mock_classify_lines.assert_called_once()
        # Should preserve original message data
        assert len(result) == 2
        assert result[0]["timestamp"] == "2024-01-01"
        assert result[1]["timestamp"] == "2024-01-02"


class TestEnsembleClassifier:
    """Test cases for EnsembleClassifier"""
    
    @patch('app.core.ensemble_classifier.TextClassifier')
    def test_init(self, mock_text_classifier):
        """Test EnsembleClassifier initialization"""
        model_configs = {
            "model1": "path/to/model1",
            "model2": "path/to/model2"
        }
        
        ensemble = EnsembleClassifier(model_configs)
        
        assert len(ensemble.members) == 2
        assert ensemble.model_names == ["model1", "model2"]
        assert mock_text_classifier.call_count == 2
    
    @patch('app.core.ensemble_classifier.TextClassifier')
    def test_load_model(self, mock_text_classifier):
        """Test EnsembleClassifier load_model method"""
        # Setup mock members
        mock_member1 = Mock()
        mock_member2 = Mock()
        mock_member1.load_model = Mock()
        mock_member2.load_model = Mock()
        
        model_configs = {"model1": "path1", "model2": "path2"}
        ensemble = EnsembleClassifier(model_configs)
        ensemble.members = [mock_member1, mock_member2]
        
        # Test async load_model
        import asyncio
        asyncio.run(ensemble.load_model())
        
        mock_member1.load_model.assert_called_once()
        mock_member2.load_model.assert_called_once()
    
    def test_classify_text_empty_results(self):
        """Test classify_text with empty results from models"""
        ensemble = EnsembleClassifier({"model1": "path1"})
        ensemble.members = [Mock()]
        ensemble.members[0].classify_text.return_value = []
        
        result = ensemble.classify_text("test text")
        assert result == []
    
    def test_classify_text_merge_results(self):
        """Test classify_text properly merges results from multiple models"""
        # Setup mock members
        mock_member1 = Mock()
        mock_member2 = Mock()
        
        mock_member1.classify_text.return_value = [
            {"text": "test", "line_number": 1, "prediction": "Relevant", "confidence": 0.9}
        ]
        mock_member2.classify_text.return_value = [
            {"text": "test", "line_number": 1, "prediction": "Non-Relevant", "confidence": 0.7}
        ]
        
        ensemble = EnsembleClassifier({"model1": "path1", "model2": "path2"})
        ensemble.members = [mock_member1, mock_member2]
        ensemble.model_names = ["model1", "model2"]
        
        result = ensemble.classify_text("test text")
        
        assert len(result) == 1
        assert result[0]["text"] == "test"
        assert result[0]["line_number"] == 1
        assert result[0]["model1_prediction"] == "Relevant"
        assert result[0]["model1_confidence"] == 0.9
        assert result[0]["model2_prediction"] == "Non-Relevant"
        assert result[0]["model2_confidence"] == 0.7
        # Generic keys should be removed
        assert "prediction" not in result[0]
        assert "confidence" not in result[0]
    
    def test_classify_standard_inputs(self):
        """Test classify_standard_inputs method"""
        mock_member = Mock()
        mock_member.classify_standard_inputs.return_value = [
            {"text": "test", "timestamp": "2024-01-01", "prediction": "Relevant", "confidence": 0.8}
        ]
        
        ensemble = EnsembleClassifier({"model1": "path1"})
        ensemble.members = [mock_member]
        ensemble.model_names = ["model1"]
        
        message_blocks = [{"text": "test", "timestamp": "2024-01-01"}]
        result = ensemble.classify_standard_inputs(message_blocks)
        
        assert len(result) == 1
        assert result[0]["timestamp"] == "2024-01-01"
        assert result[0]["model1_prediction"] == "Relevant"
        assert result[0]["model1_confidence"] == 0.8
