# app/services/whatsapp_processor.py
from datetime import timedel<PERSON>
from typing import Dict, Any, List
from pathlib import Path # <--- FIX: This import was missing
import io, csv, json

from app.platform.whatsapp_parser import WhatsAppParser
from app.utils.logger import logger
from app.services.sessionizer import Sessionizer
from app.core.text_classifier import classifier
from app.config import MODEL_PATHS

class WhatsAppProcessor:
    def __init__(self, session_gap_minutes: int, context_window_size: int):
        self.parser = WhatsAppParser()
        self.session_gap = timedelta(minutes=session_gap_minutes)
        self.context_window_size = context_window_size
        # Get model names from the config to use for stats and CSV headers
        self.model_names = list(MODEL_PATHS.keys())

    def process_whatsapp_data(self, input_path: str, *, context_aware: bool = False) -> Dict[str, Any]:
        """Main processing pipeline for structured files."""
        files = self.parser.detect_platform_files(Path(input_path))
        if not files["exports"] and not files["databases"]:
            raise ValueError("No valid WhatsApp files found in the input path")

        all_messages = []
        for f in files["exports"] + files["databases"]:
            try:
                all_messages.extend(self.parser.parse_file(f))
            except Exception as e:
                logger.warning(f"Failed to parse file {f.name}: {e}")

        if not all_messages:
            raise ValueError("No valid messages could be parsed from the input files.")

        sessions = Sessionizer(self.session_gap, self.context_window_size).create_sessions(all_messages)

        all_classified_messages = []
        for sess in sessions:
            msgs_to_classify = sess.get("messages", [])
            if not msgs_to_classify:
                continue
            
            # The classifier now returns messages with new keys like 'gbert-base_prediction'
            classified_msgs = classifier.classify_standard_inputs(msgs_to_classify, context_aware=context_aware)
            sess["messages"] = classified_msgs
            all_classified_messages.extend(classified_msgs)

        # --- CRITICAL FIX: Calculate stats based on the new multi-model structure ---
        total_relevant = 0
        for msg in all_classified_messages:
            # A message is relevant if ANY model predicts it as 'Relevant'
            is_relevant = any(
                msg.get(f"{name}_prediction") == "Relevant" for name in self.model_names
            )
            if is_relevant:
                total_relevant += 1

        stats = {
            "chats": len({m.get("chat_id") for m in all_classified_messages}),
            "sessions": len(sessions),
            "messages": len(all_classified_messages),
            "relevant_messages": total_relevant,
        }
        
        return {
            "results": sessions,
            "stats": stats,
            # CSV and JSONL generation can be handled by the route if needed
        }
