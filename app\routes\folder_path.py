# app/routes/folder_path.py

from fastapi import APIRouter, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pathlib import Path

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.utils.logger import logger
from app.config import DEFAULT_SESSION_GAP, DEFAULT_CONTEXT_WINDOW

router = APIRouter()

@router.post("/classify-folder")
async def classify_folder_api(
    folder_path: str = Form(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
    explain: bool = Form(False), # Keep explain param for consistency
):
    """
    Handles the classification of all supported files within a given folder
    and all of its subfolders.
    """
    folder = Path(folder_path)
    if not folder.is_dir():
        raise HTTPException(status_code=400, detail="Invalid folder path provided.")

    logger.info(f"Starting recursive classification for folder: {folder.resolve()}")
    
    file_outputs = {}
    errors = []

    # --- KEY CHANGE: Use rglob for recursive scanning ---
    for file_path in folder.rglob("*"):
        if not file_path.is_file():
            continue
        
        # Use relative path for cleaner keys in the response
        relative_path = file_path.relative_to(folder)
        
        try:
            logger.info(f"Processing file: {relative_path}")
            # --- Plain .txt files ---
            if file_path.suffix.lower() == ".txt":
                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                    content = f.read()
                
                results = classifier.classify_text(content, context_aware=context_aware)
                
                # Calculate stats for this file
                model_names = [k.replace("_prediction", "") for k in results[0].keys() if k.endswith("_prediction")] if results else []
                relevant_found = sum(
                    1 for r in results 
                    if any(r.get(f"{name}_prediction") == "Relevant" for name in model_names)
                )
                stats = {
                    "messages": len(results),
                    "relevant_messages": relevant_found,
                }
                file_outputs[str(relative_path)] = {"results": results, "stats": stats, "file_type": "txt"}

            # --- Structured files ---
            elif file_path.suffix.lower() in [".xlsx", ".csv", ".ods", ".db", ".sqlite"]:
                processor = BaseProcessor(
                    session_gap_minutes=session_gap_minutes,
                    context_window_size=context_window_size,
                )
                result = processor.detect_and_process(str(file_path), context_aware=context_aware)
                file_outputs[str(relative_path)] = {
                    "results": result.get("results", []), # Already sessionized
                    "stats": result.get("stats", {}),
                    "platform": result.get("platform", "unknown"),
                    "file_type": "structured",
                }
            
        except Exception as e:
            logger.error(f"Failed to process file {relative_path}: {e}", exc_info=True)
            errors.append(f"{relative_path}: {str(e)}")

    logger.info(f"Folder classification complete for: {folder.resolve()}")
    return JSONResponse(jsonable_encoder({
        "status": "completed",
        "file_outputs": file_outputs,
        "errors": errors,
    }))
