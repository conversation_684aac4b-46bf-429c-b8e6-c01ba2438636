from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime, timezone
import pandas as pd
import sqlite3
import re
from dateutil import parser as dateparser
from app.utils.logger import logger
from .base_parser import BaseSocialMediaParser
from .whatsapp_db_parser import WhatsAppDBParser

class WhatsAppParser(BaseSocialMediaParser):
    def __init__(self):
        self.valid_text_columns = ["message", "text", "nachricht", "content"]
        self.valid_sender_columns = ["sender", "from", "autor", "participant", "name", "von"]
        self.valid_time_columns = ["timestamp", "datetime", "zeit", "date"]
        self.valid_chat_columns = ["chat_id", "group", "group_name", "conversation", "thread", "an", "gruppenname", "gruppe"]

    @classmethod
    def detect_platform_files(cls, input_path: Path) -> Dict[str, List[Path]]:
        input_path = Path(input_path)
        results = {"exports": [], "databases": [], "other": []}

        for file_path in input_path.rglob("*") if input_path.is_dir() else [input_path]:
            if not file_path.is_file():
                continue

            suffix = file_path.suffix.lower()

            if suffix in [".csv", ".xlsx", ".ods"]:
                if cls()._is_valid_export_file(file_path):
                    results["exports"].append(file_path)
                else:
                    logger.info(f"Skipping unsupported export file: {file_path.name}")
                    results["other"].append(file_path)

            elif suffix in [".db", ".sqlite"]:
                if cls()._is_valid_database(file_path):
                    results["databases"].append(file_path)
                else:
                    logger.warning(f"{file_path.name} skipped: 'messages' table missing")
                    results["other"].append(file_path)
            else:
                results["other"].append(file_path)

        return results

    @classmethod
    def _is_valid_export_file(cls, file_path: Path) -> bool:
        try:
            df = cls._load_dataframe(file_path)
            if df.empty or len(df.columns) < 3:
                logger.warning(f"{file_path.name} skipped: too few columns or empty")
                return False

            df.columns = [col.strip().lower() for col in df.columns]
            cols = set(df.columns)

            has_text = any(col in cols for col in cls().valid_text_columns)
            has_time = any(col in cols for col in cls().valid_time_columns)
            has_sender = any(col in cols for col in cls().valid_sender_columns)
            has_chat = any(col in cols for col in cls().valid_chat_columns)

            logger.info(f"[VALIDATION] {file_path.name}: text={has_text}, time={has_time}, sender={has_sender}, chat={has_chat}")

            return has_text and has_time and has_sender and has_chat

        except Exception as e:
            logger.warning(f"Error validating export file {file_path.name}: {e}")
            return False

    @classmethod
    def _is_valid_database(cls, file_path: Path) -> bool:
        try:
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [t[0].lower() for t in cursor.fetchall()]
            conn.close()
            return any(t in tables for t in ("messages", "message"))
        except Exception:
            return False

    @staticmethod
    def _load_dataframe(file_path: Path) -> pd.DataFrame:
        try:
            if file_path.suffix == ".xlsx":
                return pd.read_excel(file_path, engine="openpyxl")
            elif file_path.suffix == ".ods":
                return pd.read_excel(file_path, engine="odf")
            else:
                try:
                    return pd.read_csv(file_path, encoding="utf-8")
                except UnicodeDecodeError:
                    return pd.read_csv(file_path, encoding="latin-1")
        except Exception as e:
            logger.warning(f"Failed to read file {file_path.name}: {e}")
            raise

    def parse_file(self, file_path: Path) -> List[Dict[str, Any]]:
        if file_path.suffix.lower() in [".csv", ".xlsx", ".ods"]:
            return self._parse_export_file(file_path)
        elif file_path.suffix.lower() in [".db", ".sqlite"]:
            return  WhatsAppDBParser().parse_file(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")

    def _parse_export_file(self, file_path: Path) -> List[Dict[str, Any]]:
        logger.info(f"Parsing export file '{file_path.name}'")

        if file_path.suffix.lower() == ".xlsx":
            logger.info(f"File '{file_path.name}' might be a XAMN-style export (.xlsx)")

        df = self._load_dataframe(file_path)
        df.columns = [col.strip().lower() for col in df.columns]
        logger.info(f"Columns found: {list(df.columns)}")

        chat_type = self.detect_chat_type(df)
        logger.info(f"Detected chat type: {chat_type}")

        standardized = []
        for idx, row in df.iterrows():
            try:
                message = self._normalize_export_row(row, chat_type)
                if message.get("text") and str(message["text"]).strip() and message.get("timestamp"):
                    message["source_file"] = str(file_path.name)
                    standardized.append(message)
            except Exception as e:
                logger.warning(f"Error parsing row {idx} in {file_path.name}: {e}")

        logger.info(f"Parsed {len(standardized)} valid messages from export '{file_path.name}'")
        return standardized

    def detect_chat_type(self, df: pd.DataFrame) -> str:
        senders = df[[col for col in self.valid_sender_columns if col in df.columns][0]].nunique() if any(col in df.columns for col in self.valid_sender_columns) else 0
        receivers = df[[col for col in self.valid_chat_columns if col in df.columns][0]].nunique() if any(col in df.columns for col in self.valid_chat_columns) else 0
        return "group" if senders > 1 or receivers > 1 else "single"

    def _normalize_export_row(self, row: pd.Series, chat_type: str) -> Dict[str, Any]:
        def get_col(possible_cols):
            for col in possible_cols:
                if col in row and pd.notna(row[col]):
                    return row[col]
            return None

        timestamp = get_col(self.valid_time_columns)
        parsed_time = None
        if timestamp:
            try:
                if isinstance(timestamp, (int, float)):
                    parsed_time = pd.to_datetime(timestamp, origin="1899-12-30", unit="d", errors="coerce")
                else:
                    parsed_time = dateparser.parse(str(timestamp), fuzzy=True)
            except Exception:
                parsed_time = None

        text = get_col(self.valid_text_columns)
        if isinstance(text, str):
            text = self._clean_message(text)

        return {
            "timestamp": parsed_time,
            "sender": get_col(self.valid_sender_columns) or "unknown",
            "chat_id": get_col(self.valid_chat_columns) or "unknown_chat",
            "text": text or "",
            "chat_type": chat_type,
            "platform": "whatsapp"
        }

    def _clean_message(self, text: str) -> str:
        if not text:
            return ""
        text = str(text).strip()
        patterns = [
            r"<Media omitted>", r"image omitted", r"video omitted",
            r"audio omitted", r"document omitted", r"gif omitted",
            r"message was deleted", r"deleted this message",
            r"missed voice call", r"changed the subject", r"left the group"
        ]
        for p in patterns:
            if re.search(p, text, re.IGNORECASE):
                return ""
        return text

    def normalize_message(self, *args, **kwargs):
        return {}
