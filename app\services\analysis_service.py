# app/services/analysis_service.py

import numpy as np
import faiss
from sklearn.cluster import DBSCAN
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Set
import logging

from llama_cpp import Llama # <-- Import Llama

from app.config import (
    EMBEDDING_MODEL_PATH, 
    LOCAL_LLM_PATH, 
    ANALYSIS_RELEVANCE_THRESHOLD,
    MODEL_PATHS
)

logger = logging.getLogger(__name__)

class AnalysisService:
    def __init__(self):
        logger.info("Initializing AnalysisService...")
        self.model_names = list(MODEL_PATHS.keys())
        try:
            self.embedding_model: SentenceTransformer = SentenceTransformer(EMBEDDING_MODEL_PATH)
            
            # --- CHANGE: Load the local LeoLM model instead of the pipeline ---
            logger.info(f"Loading German LeoLM model from: {LOCAL_LLM_PATH}")
            self.summarizer: Llama = Llama(
                model_path=LOCAL_LLM_PATH,
                n_ctx=4096,       # Context window size
                n_gpu_layers=-1,  # Offload all layers to GPU if available
                verbose=False     # Set to True for detailed Llama C++ logs
            )
            logger.info("AnalysisService models (embedding and LeoLM) loaded successfully.")
        except Exception as e:
            logger.error(f"Failed to load analysis models: {e}", exc_info=True)
            raise

    # The cluster_and_summarize method has one key change in how it calls the model.
    def cluster_and_summarize(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        print("\n" + "="*80)
        print("🚀 STARTING ADVANCED RAG ANALYSIS WITH GERMAN LEO-LM")
        print("="*80)
        
        relevant_messages = self._filter_and_deduplicate(messages)
        print(f"   [Step 1] Filtered to {len(relevant_messages)} unique relevant messages.")

        if len(relevant_messages) < 2:
            return {"status": "completed", "summary": "Not enough relevant messages to create topic clusters.", "clusters": []}
            
        all_texts = [msg.get('text', '') for msg in messages]
        all_embeddings = self.embedding_model.encode(all_texts, convert_to_tensor=False, show_progress_bar=False)
        faiss.normalize_L2(all_embeddings)
        index = faiss.IndexFlatIP(all_embeddings.shape[1])
        index.add(all_embeddings)
        print(f"   [Step 2] Created vector DB of all {len(messages)} messages.")

        relevant_texts = [msg.get('text', '') for msg in relevant_messages]
        relevant_embeddings = self.embedding_model.encode(relevant_texts, convert_to_tensor=False, show_progress_bar=False)
        faiss.normalize_L2(relevant_embeddings)
        
        k = 5
        distances, indices = index.search(relevant_embeddings, k)
        print(f"   [Step 3] Retrieved top {k} context messages for each relevant message.")

        context_embeddings = np.array([np.mean(all_embeddings[i], axis=0) for i in indices])
        faiss.normalize_L2(context_embeddings)
        
        clusters = DBSCAN(eps=0.15, min_samples=2, metric="cosine").fit_predict(context_embeddings)
        unique_clusters = set(clusters)
        num_clusters_found = len(unique_clusters) - (1 if -1 in unique_clusters else 0)
        print(f"   [Step 4] Clustered evidence into {num_clusters_found} topics.")

        analysis_results = []
        print("\n" + "-"*80 + "\n🧠 SUMMARIZING TOPICS WITH GERMAN LEO-LM\n" + "-"*80)
        
        for cluster_id in sorted(unique_clusters):
            if cluster_id == -1: continue

            print(f"\n   [Topic Cluster ID: {cluster_id}]")
            cluster_indices = [i for i, cid in enumerate(clusters) if cid == cluster_id]
            key_evidence_messages = [relevant_messages[i] for i in cluster_indices]
            
            context_indices_for_cluster: Set[int] = set()
            for i in cluster_indices:
                context_indices_for_cluster.update(indices[i])
            
            full_context_messages = sorted([messages[i] for i in context_indices_for_cluster], key=lambda x: x.get('timestamp', ''))
            
            prompt = self._build_summary_prompt(key_evidence_messages, full_context_messages)
            
            try:
                # --- CHANGE: How we call the model and get the response ---
                response = self.summarizer.create_chat_completion(
                    messages=[
                        {"role": "system", "content": "Du bist ein hochqualifizierter forensischer Analyst des deutschen BKA. Deine Aufgabe ist es, eine kurze, analytische Zusammenfassung potenzieller rechtswidriger Aktivitäten auf der Grundlage der bereitgestellten Chat-Nachrichten zu erstellen."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=256 # Limit the length of the summary
                )
                summary_text = response['choices'][0]['message']['content'].strip()
                print(f"   > ✅ LeoLM Summary: \"{summary_text}\"")
            except Exception as e:
                logger.error(f"LeoLM summarization failed for cluster {cluster_id}: {e}")
                summary_text = "Zusammenfassung für dieses Thema konnte nicht erstellt werden."
                print(f"   > ❌ LeoLM summarization failed: {e}")

            analysis_results.append({
                "cluster_id": f"Thema {cluster_id + 1}",
                "summary": summary_text,
                "message_count": len(key_evidence_messages),
                "messages": key_evidence_messages,
            })
            
        report = {"status": "completed", "summary": f"{len(analysis_results)} unterschiedliche relevante Themen identifiziert.", "clusters": analysis_results}
        print("\n" + "="*80 + "\n✅ ADVANCED RAG ANALYSIS COMPLETE\n" + "="*80 + "\n")
        return report

    def _build_summary_prompt(self, key_messages: List[Dict], context_messages: List[Dict]) -> str:
        """Constructs the prompt for the LeoLM model in German."""
        prompt = "Analysieren Sie das folgende Chat-Protokoll. Überprüfen Sie zuerst die SCHLÜSSELBEWEISE und verwenden Sie dann die UMGEBENDE KONVERSATION für den Kontext. Identifizieren und fassen Sie alle gefundenen eindeutigen rechtswidrigen Themen zusammen. Seien Sie prägnant und analytisch.\n\n"
        prompt += "[SCHLÜSSELBEWEISE]:\n"
        for msg in key_messages:
            prompt += f"- {msg.get('text', '')}\n"
        prompt += "\n[UMGEBENDE KONVERSATION]:\n"
        for msg in context_messages:
            prompt += f"- {msg.get('text', '')}\n"
        prompt += "\n[ANALYTISCHE ZUSAMMENFASSUNG]:"
        return prompt

    def _filter_and_deduplicate(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filters for relevant messages and ensures each unique message appears only once."""
        deduplicated_messages: Dict[str, Dict[str, Any]] = {}
        for msg in messages:
            is_relevant = any(
                msg.get(f"{name}_prediction") == 'Relevant' and 
                msg.get(f"{name}_confidence", 0) >= ANALYSIS_RELEVANCE_THRESHOLD
                for name in self.model_names
            )
            if is_relevant:
                message_text = msg.get('text', '')
                if message_text not in deduplicated_messages:
                    deduplicated_messages[message_text] = msg
        
        return list(deduplicated_messages.values())

# Create a singleton instance for use across the application.
# The LeoLM model will be loaded into memory when the FastAPI server starts.
analysis_service = AnalysisService()
