# Unit Tests for Forensic Text Classification API

This directory contains comprehensive unit tests for the Forensic Text Classification API project.

## Test Structure

```
tests/unit_tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Pytest fixtures and configuration
├── test_text_classifier.py     # Tests for core classification models
├── test_whatsapp_parser.py     # Tests for WhatsApp parsers
├── test_services.py            # Tests for service classes
├── test_utils.py               # Tests for utility functions
├── test_routes.py              # Tests for API routes
├── test_integration.py         # Integration tests
└── README.md                   # This file
```

## Test Categories

### Core Classification Tests (`test_text_classifier.py`)
- **TextClassifier**: Single model classification logic
- **EnsembleClassifier**: Multi-model ensemble functionality
- Model loading, text processing, and prediction merging

### Parser Tests (`test_whatsapp_parser.py`)
- **WhatsAppParser**: Export file parsing (.xlsx, .csv, .ods)
- **WhatsAppDBParser**: Database parsing (.db, .sqlite)
- File validation, message normalization, timestamp parsing

### Service Tests (`test_services.py`)
- **Sessionizer**: Message grouping by time gaps
- **BaseProcessor**: Platform detection and routing
- **WhatsAppProcessor**: WhatsApp-specific processing pipeline
- **AnalysisService**: RAG analysis and health checks

### Utility Tests (`test_utils.py`)
- **Output Utils**: CSV/JSONL conversion, file saving
- **Logger**: Logging configuration and functionality
- **Conversion Utils**: Data type conversion helpers

### API Route Tests (`test_routes.py`)
- **Raw Text Routes**: Text classification endpoints
- **File Upload Routes**: Single file processing
- **Folder Path Routes**: Batch folder processing
- Error handling and response validation

### Integration Tests (`test_integration.py`)
- End-to-end processing pipelines
- Component interaction testing
- Error propagation through the system

## Running Tests

### Prerequisites

```bash
# Install test dependencies
pip install pytest pytest-cov pytest-mock

# Or install all project dependencies
pip install -r requirements.txt
```

### Basic Test Execution

```bash
# Run all unit tests
python -m pytest tests/unit_tests/

# Run with coverage report
python -m pytest tests/unit_tests/ --cov=app --cov-report=html

# Run specific test file
python -m pytest tests/unit_tests/test_text_classifier.py

# Run specific test function
python -m pytest tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_init_with_valid_path
```

### Using the Test Runner Script

```bash
# Run all tests with coverage
python tests/run_tests.py

# Run specific test file
python tests/run_tests.py --test test_text_classifier.py

# Run without coverage
python tests/run_tests.py --no-coverage

# Verbose output
python tests/run_tests.py --verbose

# List available test files
python tests/run_tests.py --list-tests
```

### Test Markers

Tests are categorized using pytest markers:

```bash
# Run only unit tests
python -m pytest -m unit

# Run only integration tests  
python -m pytest -m integration

# Run only API tests
python -m pytest -m api

# Run only parser tests
python -m pytest -m parser
```

## Test Fixtures

The `conftest.py` file provides shared fixtures:

- **`temp_dir`**: Temporary directory for file operations
- **`sample_messages`**: Sample message data for testing
- **`sample_whatsapp_xlsx`**: Sample WhatsApp Excel file
- **`sample_whatsapp_db`**: Sample WhatsApp database file
- **`mock_text_classifier`**: Mock classifier for testing
- **`mock_analysis_service`**: Mock analysis service
- **`sample_text_file`**: Sample text file

## Mocking Strategy

Tests use extensive mocking to isolate components:

- **Model Loading**: Mock heavy ML model loading
- **File I/O**: Mock file operations for speed
- **External Dependencies**: Mock transformers, torch, etc.
- **Database Operations**: Mock SQLite connections

## Coverage Goals

- **Minimum Coverage**: 70% overall
- **Critical Components**: 90%+ coverage for core classifiers and parsers
- **API Routes**: 85%+ coverage for all endpoints
- **Utilities**: 80%+ coverage for helper functions

## Writing New Tests

### Test Naming Convention
- Test files: `test_<module_name>.py`
- Test classes: `Test<ClassName>`
- Test methods: `test_<functionality>`

### Example Test Structure

```python
class TestMyComponent:
    """Test cases for MyComponent"""
    
    def test_init(self):
        """Test component initialization"""
        component = MyComponent()
        assert component is not None
    
    @patch('module.dependency')
    def test_method_with_mock(self, mock_dependency):
        """Test method with mocked dependency"""
        mock_dependency.return_value = "expected"
        component = MyComponent()
        result = component.method()
        assert result == "expected"
    
    def test_error_handling(self):
        """Test error handling"""
        component = MyComponent()
        with pytest.raises(ValueError):
            component.method_that_should_fail()
```

### Best Practices

1. **Isolation**: Each test should be independent
2. **Mocking**: Mock external dependencies and heavy operations
3. **Assertions**: Use specific assertions with clear error messages
4. **Edge Cases**: Test boundary conditions and error scenarios
5. **Documentation**: Include docstrings explaining test purpose

## Continuous Integration

Tests are designed to run in CI environments:

- **Fast Execution**: Heavy models are mocked
- **No External Dependencies**: All external services mocked
- **Deterministic**: Tests produce consistent results
- **Comprehensive**: Cover all critical code paths

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure project root is in Python path
2. **Mock Failures**: Check mock patch paths match actual imports
3. **Fixture Errors**: Verify fixture dependencies in conftest.py
4. **Coverage Issues**: Check for untested code paths

### Debug Mode

```bash
# Run with debug output
python -m pytest tests/unit_tests/ -v -s

# Run single test with full output
python -m pytest tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_init_with_valid_path -v -s

# Drop into debugger on failure
python -m pytest tests/unit_tests/ --pdb
```

## Contributing

When adding new functionality:

1. Write tests first (TDD approach)
2. Ensure new tests pass
3. Maintain or improve coverage percentage
4. Update this README if adding new test categories
5. Follow existing naming and structure conventions
