"""
Essential integration tests
"""
import pytest
from datetime import datetime, timezone, timedelta

from app.services.sessionizer import Sessionizer


class TestBasicIntegration:
    """Essential integration tests"""

    def test_sessionizer_with_real_data(self):
        """Test sessionizer with realistic data"""
        messages = [
            {
                "timestamp": datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
                "text": "Message 1",
                "sender": "<PERSON>"
            },
            {
                "timestamp": datetime(2024, 1, 1, 10, 5, 0, tzinfo=timezone.utc),
                "text": "Message 2",
                "sender": "<PERSON>"
            },
            {
                "timestamp": datetime(2024, 1, 1, 10, 30, 0, tzinfo=timezone.utc),  # New session
                "text": "Message 3",
                "sender": "Alice"
            }
        ]

        sessionizer = Sessionizer(session_gap=timedelta(minutes=10))
        sessions = sessionizer.sessionize(messages)

        # Should create 2 sessions due to time gap
        assert len(sessions) == 2
        assert sessions[0]["session_id"] == 1
        assert sessions[1]["session_id"] == 2
        assert len(sessions[0]["messages"]) == 2
        assert len(sessions[1]["messages"]) == 1
