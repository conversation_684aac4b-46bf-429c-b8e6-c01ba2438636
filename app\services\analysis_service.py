# app/services/analysis_service.py

import numpy as np
import faiss
from sklearn.cluster import AgglomerativeClustering
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any
import logging
from pathlib import Path

from llama_cpp import Llama
from app.config import (
    EMBEDDING_MODEL_PATH,
    LOCAL_LLM_PATH,
    ANALYSIS_RELEVANCE_THRESHOLD,
    MODEL_PATHS,
    DBSCAN_MIN_SAMPLES,
    RAG_NEIGHBORS,
    AGGLOMERATIVE_THRESHOLD
)

logger = logging.getLogger(__name__)

def _chunk_messages(messages: List[Dict[str, Any]], max_tokens: int = 512) -> List[List[Dict[str, Any]]]:
    chunks = []; current_chunk = []; current_length = 0
    CHUNK_WORDS = int(max_tokens * 0.78)
    for msg in messages:
        text = msg.get('text', '') or ''; words = text.split()
        if current_length + len(words) > CHUNK_WORDS and current_chunk:
            chunks.append(current_chunk); current_chunk = []; current_length = 0
        current_chunk.append(msg); current_length += len(words)
    if current_chunk: chunks.append(current_chunk)
    return chunks

class AnalysisService:
    def __init__(self):
        self.model_names = list(MODEL_PATHS.keys()); self.embedding_model: SentenceTransformer | None = None
        self.summarizer: Llama | None = None; self._initialized = False

    def _ensure_models_loaded(self):
        if self._initialized: return
        logger.info("Initializing AnalysisService models...")
        try:
            if not Path(LOCAL_LLM_PATH).exists(): raise FileNotFoundError(f"LLM model file not found: {LOCAL_LLM_PATH}")
            self.embedding_model = SentenceTransformer(EMBEDDING_MODEL_PATH)
            logger.info(f"Loading German LeoLM model from: {LOCAL_LLM_PATH}")
            self.summarizer = Llama(
                model_path=LOCAL_LLM_PATH, n_ctx=4096, n_gpu_layers=-1, verbose=False, temperature=0.1)
            self._initialized = True; logger.info("AnalysisService models loaded successfully.")
        except Exception as e:
            logger.error(f"Failed to load analysis models: {e}", exc_info=True); raise

    def cluster_and_summarize(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Orchestrates the pipeline using the final, most robust strategy of clustering conversation contexts."""
        self._ensure_models_loaded()
        print("\n" + "="*80 + "\n🚀 STARTING CONTEXT-AWARE CLUSTERING ANALYSIS\n" + "="*80)

        relevant_messages = self._filter_and_deduplicate(messages)
        if len(relevant_messages) < DBSCAN_MIN_SAMPLES:
            return {"status": "completed", "summary": "Not enough relevant messages.", "clusters": []}
        
        print(f"\n   [Step 1] COMPLETE: Found {len(relevant_messages)} unique relevant messages.")
        
        # We create a vector DB of all messages to retrieve context from
        index, all_embeddings = self._create_vector_db(messages)
        
        # We retrieve the context for each relevant message
        context_indices, _ = self._retrieve_context(index, relevant_messages)

        # We cluster the contexts using a more robust clustering method
        clusters = self._cluster_contexts(context_indices, all_embeddings)
        
        analysis_results = self._summarize_clusters(
            clusters=clusters, relevant_messages=relevant_messages)

        report = {"status": "completed", "summary": f"{len(analysis_results)} unterschiedliche relevante Themen identifiziert.", "clusters": analysis_results}
        print("\n" + "="*80 + "\n✅ CONTEXT-AWARE ANALYSIS COMPLETE\n" + "="*80 + "\n")
        return report

    def _filter_and_deduplicate(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filters out non-relevant messages and deduplicates based on text content."""
        print("   [Step 1a] Filtering and standardizing relevant messages...")
        standardized_relevant_messages = []; seen_texts = set()
        for msg in messages:
            is_relevant = any(
                msg.get(f"{name}_prediction") == 'Relevant' and msg.get(f"{name}_confidence", 0) >= ANALYSIS_RELEVANCE_THRESHOLD
                for name in self.model_names)
            message_text = msg.get('text') or msg.get('input_snippet') or ''
            if is_relevant and message_text and message_text not in seen_texts:
                clean_msg = msg.copy(); clean_msg['text'] = message_text
                standardized_relevant_messages.append(clean_msg); seen_texts.add(message_text)
        return standardized_relevant_messages

    def _create_vector_db(self, messages: List[Dict[str, Any]]) -> tuple[faiss.Index, np.ndarray]:
        """Creates a FAISS vector database from all message embeddings."""
        all_texts = [msg.get('text') or msg.get('input_snippet') or '' for msg in messages]
        all_embeddings = self.embedding_model.encode(all_texts, convert_to_tensor=False, show_progress_bar=False)
        faiss.normalize_L2(all_embeddings)
        index = faiss.IndexFlatIP(all_embeddings.shape[1]); index.add(all_embeddings)
        print(f"   [Step 2] COMPLETE: Created vector DB of all {len(messages)} messages.")
        return index, all_embeddings

    def _retrieve_context(self, index: faiss.Index, relevant_messages: List[Dict[str, Any]]) -> tuple[np.ndarray, np.ndarray]:
        """Retrieves context for each relevant message."""
        relevant_texts = [msg.get('text', '') for msg in relevant_messages]
        relevant_embeddings = self.embedding_model.encode(relevant_texts, convert_to_tensor=False, show_progress_bar=False)
        faiss.normalize_L2(relevant_embeddings)
        k = min(RAG_NEIGHBORS, index.ntotal)
        _, indices = index.search(relevant_embeddings, k)
        print(f"   [Step 3] COMPLETE: Retrieved context for {len(relevant_messages)} relevant messages.")
        return indices, relevant_embeddings
    

    def _cluster_contexts(self, context_indices: np.ndarray, all_embeddings: np.ndarray) -> np.ndarray:
        """
        Creates an average embedding for each context (mini-conversation) and clusters those.
        This is much more stable than clustering individual messages.
        """
        # Calculate the mean embedding for each context
        context_embeddings = np.array([
            all_embeddings[indices].mean(axis=0) for indices in context_indices
        ])
        
        # Now cluster these more stable "context embeddings"
        clusterer = AgglomerativeClustering(
            n_clusters=None,
            distance_threshold=AGGLOMERATIVE_THRESHOLD,
            metric='cosine',
            linkage='average'
        )
        clusters = clusterer.fit_predict(context_embeddings)
        
        num_clusters_found = len(set(clusters))
        print(f"   [Step 4] COMPLETE: Clustered conversation contexts into {num_clusters_found} topics with threshold={AGGLOMERATIVE_THRESHOLD}.")
        return clusters
    

    def _build_summary_prompt(self, context_messages: List[Dict]) -> str:
        prompt = (
            "Sie sind ein präziser und sachlicher forensischer Analyst. Ihre einzige Aufgabe ist es, die Fakten aus dem folgenden Beweismaterial-Cluster zusammenzufassen.\n\n"
            "**BEFEHLE:**\n"
            "1. Analysieren Sie NUR die folgenden Chat-Nachrichten dieses Clusters.\n"
            "2. Identifizieren Sie das zentrale Thema des Clusters (z. B. Drogenhandel, Betrug, unangemessene Kommunikation).\n"
            "3. Nennen Sie die exakten Namen der beteiligten Personen in diesem Thema.\n"
            "4. **ACHTUNG:** Achten Sie genau darauf, wer spricht (z.B. 'David:') und wer antwortet, um die Rollen korrekt zu bestimmen (wer fragt, wer antwortet).\n"
            "5. Zitieren Sie direkt die wichtigsten Wörter oder Sätze als Beweis.\n"
            "6. **VERBOTEN:** Spekulieren Sie nicht. Raten Sie nicht. Fügen Sie keine Informationen hinzu, die nicht explizit im Text stehen.\n\n"
        )
        prompt += "[BEWEISMITTEL IN DIESEM CLUSTER]:\n"
        for msg in context_messages:
            prompt += f"- {msg.get('text', '')}\n"
        prompt += "\n[SACHLICHE ANALYTISCHE ZUSAMMENFASSUNG DIESES THEMAS]:"
        return prompt

    def _get_llm_completion(self, prompt: str, max_tokens: int) -> str:
        try:
            response = self.summarizer.create_completion(prompt=prompt, max_tokens=max_tokens, stop=["</s>", "\n\n["], temperature=0.1)
            return response['choices'][0]['text'].strip()
        except Exception as e:
            logger.error(f"LeoLM summarization failed: {e}"); return f"Zusammenfassung konnte nicht erstellt werden. Fehler: {str(e)}"

    def _summarize_clusters(self, clusters: np.ndarray, relevant_messages: List[Dict]) -> List[Dict]:
        analysis_results = []; print("\n" + "-"*80 + "\n🧠 SUMMARIZING CLUSTERS WITH STRICT PROMPTS\n" + "-"*80)
        unique_cluster_ids = sorted(list(set(clusters)))
        for cluster_id in unique_cluster_ids:
            if cluster_id == -1: continue
            print(f"\n   [Topic Cluster ID: {cluster_id}]")
            messages_in_cluster = [relevant_messages[i] for i, cid in enumerate(clusters) if cid == cluster_id]
            chunked_cluster = _chunk_messages(messages_in_cluster, max_tokens=512)
            chunk_summaries = []; print(f"     > Topic has {len(messages_in_cluster)} messages, split into {len(chunked_cluster)} chunk(s).")
            system_prompt = "Sie sind ein hochpräziser forensischer BKA-Analyst. Halten Sie sich strikt an die BEFEHLE im User-Prompt. Geben Sie NUR eine sachliche Zusammenfassung der Fakten aus."
            for i, chunk in enumerate(chunked_cluster):
                user_prompt = self._build_summary_prompt(chunk)
                summary_text = self._get_llm_completion(prompt=system_prompt + user_prompt, max_tokens=400)
                print(f"     > ✅ Chunk {i+1}/{len(chunked_cluster)} summary generated.")
                chunk_summaries.append(summary_text)
            if len(chunk_summaries) > 1:
                final_prompt_text = "Fasse die folgenden Teilanalysen zu einer einzigen, kohärenten Gesamtanalyse des Themas zusammen:\n\n" + "\n".join(f"- {s}" for s in chunk_summaries)
                final_summary = self._get_llm_completion(prompt=final_prompt_text, max_tokens=512)
                print("     > ✅ Final synthesized summary generated for topic.")
            else:
                final_summary = chunk_summaries[0] if chunk_summaries else ""
            analysis_results.append({"cluster_id": f"Thema {cluster_id + 1}", "summary": final_summary, "message_count": len(messages_in_cluster), "messages": messages_in_cluster})
        return analysis_results

analysis_service = AnalysisService()