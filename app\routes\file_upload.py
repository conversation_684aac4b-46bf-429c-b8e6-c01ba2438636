# app/routes/file_upload.py

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder           # ← serialises datetime
from pathlib import Path
import tempfile
import shutil

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.utils.logger import logger
from app.config import DEFAULT_SESSION_GAP, DEFAULT_CONTEXT_WINDOW

router = APIRouter()

@router.post("/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
    explain: bool = Form(False),
):
    """
    Handles the upload and classification of a single file.
    It distinguishes between plain text files and structured files.
    """
    temp_dir = Path(tempfile.mkdtemp())
    try:
        file_path = temp_dir / file.filename
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)

        # --- Plain .txt files ---
        if file_path.suffix.lower() == ".txt":
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
            
            results = classifier.classify_text(content, context_aware=context_aware)
            
            if explain:
                for r in results:
                    r["explanation"] = classifier.explain_with_interpret(r["text"])

            return JSONResponse(jsonable_encoder({
                "status": "completed",
                "file_type": "txt",
                "results": results,
            }))

        # --- Structured files (.xlsx, .db, etc.) ---
        processor = BaseProcessor(
            session_gap_minutes=session_gap_minutes,
            context_window_size=context_window_size,
        )
        result = processor.detect_and_process(str(file_path), context_aware=context_aware)

        return JSONResponse(jsonable_encoder({
            "status": "completed",
            "file_type": "structured",
            "platform": result.get("platform", "unknown"),
            "stats": result.get("stats", {}),
            "results": result.get("results", []), # Results are already sessionized
        }))

    except Exception as e:
        logger.error(f"File upload failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An internal error occurred: {e}")

    finally:
        if temp_dir.exists():
            shutil.rmtree(temp_dir, ignore_errors=True)
