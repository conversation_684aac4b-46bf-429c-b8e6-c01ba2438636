# Essential Unit Tests

Simple, focused unit tests for the Forensic Text Classification API.

## Test Files

```
tests/unit_tests/
├── conftest.py                 # Test fixtures
├── test_text_classifier.py     # Core classification tests
├── test_whatsapp_parser.py     # Parser tests
├── test_services.py            # Service tests
├── test_utils.py               # Utility tests
├── test_routes.py              # API route tests
├── test_integration.py         # Basic integration tests
└── README.md                   # This file
```

## What's Tested

- **TextClassifier & EnsembleClassifier**: Basic initialization and functionality
- **WhatsApp Parsers**: File detection, parsing, and validation
- **Services**: Sessionizer, BaseProcessor, AnalysisService essentials
- **Utilities**: Output formatting and logging
- **API Routes**: Basic endpoint functionality and error handling
- **Integration**: Simple component interaction tests

## Running Tests

### Quick Start

```bash
# Install pytest
pip install pytest

# Run all tests
python -m pytest tests/unit_tests/

# Run with verbose output
python -m pytest tests/unit_tests/ -v

# Run specific test file
python -m pytest tests/unit_tests/test_text_classifier.py
```

### Using Test Runner

```bash
# Simple run
python tests/run_tests.py

# Verbose output
python tests/run_tests.py --verbose
```

## Key Features

- **Fast**: Heavy models are mocked
- **Simple**: Focus on essential functionality only
- **Isolated**: Each test is independent
- **Clear**: Easy to understand and maintain

## Test Structure

Each test file focuses on one main component:
- Initialization tests
- Basic functionality tests
- Error handling tests
- Essential integration tests

Tests use mocking to avoid loading heavy ML models and external dependencies.
