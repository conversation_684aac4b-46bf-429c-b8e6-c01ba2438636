#!/usr/bin/env python3
"""
Simple test runner for essential unit tests
"""
import sys
import subprocess

def run_tests(verbose=False):
    """Run essential unit tests"""
    cmd = ["python", "-m", "pytest", "tests/unit_tests/"]

    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")

    cmd.extend(["--tb=short", "--disable-warnings"])

    print(f"Running: {' '.join(cmd)}")
    print("-" * 40)

    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted")
        return 1

def main():
    """Main function"""
    import argparse
    parser = argparse.ArgumentParser(description="Run essential unit tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    args = parser.parse_args()

    return run_tests(verbose=args.verbose)

if __name__ == "__main__":
    sys.exit(main())
