from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Import routers
from app.routes.raw_text import router as raw_text_router
from app.routes.file_upload import router as file_upload_router
from app.routes.folder_path import router as folder_path_router

# Import model loader and logger
from app.core.text_classifier import load_model
from app.utils.logger import logger

app = FastAPI(
    title="Forensic Chat Classification API",
    description="Classifies messages using BERT-based NLP model with WhatsApp pipeline",
    version="1.0.0"
)

# --- CORS Middleware ---
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- API Routers ---
app.include_router(raw_text_router, prefix="/api", tags=["Raw Text"])
app.include_router(file_upload_router, prefix="/api", tags=["File Upload"])
app.include_router(folder_path_router, prefix="/api", tags=["Folder Classification"])

# --- Lifecycle Events ---
@app.on_event("startup")
async def startup_event():
    logger.info("Starting up and loading the classification model...")
    await load_model()
    logger.info("Model loaded successfully. API is ready.")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down the API...")
