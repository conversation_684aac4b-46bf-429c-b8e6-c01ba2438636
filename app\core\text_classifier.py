import os
import torch
from typing import Dict, List, Any, Tuple
from transformers import <PERSON>Tokenizer, AutoModelForSequenceClassification
from transformers_interpret import SequenceClassificationExplainer

from app.config import MODEL_PATH, MAX_SEQUENCE_LENGTH
from app.core.exceptions import ClassificationError
import logging

logger = logging.getLogger(__name__)

class TextClassifier:
    def __init__(self) -> None:
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model: AutoModelForSequenceClassification | None = None
        self.tokenizer: AutoTokenizer | None = None
        self.labels = ["Non-Relevant", "Relevant"]
        self._explain_cache: dict[Tuple[str, int | None], List[tuple]] = {}

    # ──────────────────────────── Model init ─────────────────────────────
    async def load_model(self) -> None:
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
            self.model = (
                AutoModelForSequenceClassification.from_pretrained(MODEL_PATH)
                .to(self.device)
                .eval()
            )
            logger.info("Classifier model loaded on %s", self.device)
        except Exception as e:
            logger.error("Failed to load model: %s", e)
            raise ClassificationError(f"Model initialization failed: {e}") from e

    # ──────────────────────── Public entry points ────────────────────────
    def classify_text(
        self, text: str, context_aware: bool = False
    ) -> List[Dict[str, Any]]:
        if not text.strip():
            raise ClassificationError("Empty text provided")

        lines = [ln.strip() for ln in text.splitlines() if ln.strip()]
        if context_aware:
            return self._classify_with_context_window(lines)
        return self._classify_lines(lines)

    def classify_standard_inputs(
        self, message_blocks: List[Dict[str, Any]], context_aware: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Helper for the WhatsApp processor: `message_blocks` is a list of dicts
        that already contains sender / chat_id / timestamp metadata.
        """
        lines = [
            (blk.get("context") or blk.get("text", "")) if context_aware else blk.get("text", "")
            for blk in message_blocks
            if blk.get("text")
        ]
        classified = self._classify_lines(lines)

        output: List[Dict[str, Any]] = []
        for i, res in enumerate(classified):
            meta = message_blocks[i] if i < len(message_blocks) else {}
            res.update(
                {
                    **meta,
                    "id": i,
                    "context_input": res.get("input_snippet", ""),
                    "text": meta.get("text", ""),
                }
            )
            output.append(res)
        return output

    def classify_file_or_folder(
        self, path: str, context_aware: bool = False
    ) -> Dict[str, Any]:
        """
        Convenience method used by CLI scripts: walk a folder and classify
        every `.txt` that isn't already a results file.
        """
        path = os.path.abspath(path)
        if not os.path.exists(path):
            raise ClassificationError(f"Path not found: {path}")

        results: List[Dict[str, Any]] = []
        if os.path.isdir(path):
            for root, _, files in os.walk(path):
                for fname in files:
                    if fname.lower().endswith(".txt") and not fname.startswith("results_"):
                        results.extend(
                            self._classify_file(os.path.join(root, fname), context_aware)
                        )
        elif path.lower().endswith(".txt"):
            results = self._classify_file(path, context_aware)

        return {
            "status": "completed",
            "found": len(results),
            "results": results,
            "processed_path": path,
        }

    # ───────────────────── Explainability (cached) ──────────────────────
    def explain_with_interpret(
        self, text: str, target_index: int | None = None
    ) -> List[tuple]:
        """
        Returns a list of (token, contribution score) tuples.
        The result is cached; repeated calls with the same `text`
        and `target_index` are instant.
        """
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("Model not loaded")

        key = (text, target_index)
        if key in self._explain_cache:
            return self._explain_cache[key]

        explainer = SequenceClassificationExplainer(self.model, self.tokenizer)
        attributions = explainer(text, index=target_index)
        # cache & return
        self._explain_cache[key] = attributions
        return attributions

    # ─────────────────────── internal helpers ───────────────────────────
    def _classify_file(
        self, file_path: str, context_aware: bool
    ) -> List[Dict[str, Any]]:
        with open(file_path, encoding="utf-8", errors="ignore") as f:
            lines = [ln.strip() for ln in f if ln.strip()]

        if context_aware:
            chunks = [
                f"{lines[i - 1] if i > 0 else ''} [SEP] {lines[i]} [SEP] "
                f"{lines[i + 1] if i + 1 < len(lines) else ''}"
                for i in range(len(lines))
            ]
            classified = self._classify_lines(chunks)
            for i, res in enumerate(classified):
                res.update(
                    {
                        "file": file_path,
                        "line_number": i + 1,
                        "input_snippet": chunks[i][:150] + "..." if len(chunks[i]) > 150 else chunks[i],
                        "text": lines[i],
                    }
                )
        else:
            classified = self._classify_lines(lines)
            for i, res in enumerate(classified):
                res.update(
                    {
                        "file": file_path,
                        "line_number": i + 1,
                        "input_snippet": lines[i][:150] + "..." if len(lines[i]) > 150 else lines[i],
                        "text": lines[i],
                    }
                )
        return classified

    def _classify_lines(self, lines: List[str]) -> List[Dict[str, Any]]:
        results: List[Dict[str, Any]] = []

        for line in lines:
            inputs = self.tokenizer(
                line,
                truncation=True,
                padding=True,
                return_tensors="pt",
                max_length=MAX_SEQUENCE_LENGTH,
            ).to(self.device)

            with torch.no_grad():
                logits = self.model(**inputs).logits

            probs = torch.softmax(logits, dim=1)[0]
            pred_idx = int(torch.argmax(probs))
            results.append(
                {
                    "input_snippet": line[:150] + "..." if len(line) > 150 else line,
                    "prediction": self.labels[pred_idx],
                    "confidence": round(float(probs[pred_idx]), 4),
                }
            )
        return results

    def _classify_with_context_window(self, lines: List[str]) -> List[Dict[str, Any]]:
        chunks = [
            f"{lines[i - 1] if i > 0 else ''} [SEP] {lines[i]} [SEP] "
            f"{lines[i + 1] if i + 1 < len(lines) else ''}".strip()
            for i in range(len(lines))
        ]

        results: List[Dict[str, Any]] = []
        for chunk in chunks:
            inputs = self.tokenizer(
                chunk,
                truncation=True,
                padding=True,
                return_tensors="pt",
                max_length=MAX_SEQUENCE_LENGTH,
            ).to(self.device)

            with torch.no_grad():
                logits = self.model(**inputs).logits

            probs = torch.softmax(logits, dim=1)[0]
            pred_idx = int(torch.argmax(probs))
            results.append(
                {
                    "input_snippet": chunk[:150] + "..." if len(chunk) > 150 else chunk,
                    "prediction": self.labels[pred_idx],
                    "confidence": round(float(probs[pred_idx]), 4),
                }
            )
        return results


# ───────────────────────── module-level helpers ─────────────────────────
classifier = TextClassifier()

async def load_model() -> None:
    """FastAPI startup hook"""
    await classifier.load_model()

def classify_text(text: str, context_aware: bool = False):
    return classifier.classify_text(text, context_aware)

def classify_file_or_folder(path: str, context_aware: bool = False):
    return classifier.classify_file_or_folder(path, context_aware)

def classify_standard_inputs(blocks: List[Dict[str, Any]], context_aware: bool = False):
    return classifier.classify_standard_inputs(blocks, context_aware)
