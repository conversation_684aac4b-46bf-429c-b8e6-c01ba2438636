# app/routes/file_upload.py

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pathlib import Path
import tempfile
import shutil

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.services.analysis_service import analysis_service # <-- IMPORT THE NEW SERVICE
from app.utils.logger import logger
from app.config import DEFAULT_SESSION_GAP, DEFAULT_CONTEXT_WINDOW

router = APIRouter()

@router.post("/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
):
    """
    Handles the upload of a single file, classifies its content, and then
    performs RAG analysis (clustering and summarization) on the results.
    """
    temp_dir = Path(tempfile.mkdtemp())
    try:
        file_path = temp_dir / file.filename
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)

        # --- Step 1: Classification ---
        all_messages = []
        if file_path.suffix.lower() == ".txt":
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
            all_messages = classifier.classify_text(content, context_aware=context_aware)
        else: # Structured files
            processor = BaseProcessor(session_gap_minutes, context_window_size)
            result = processor.detect_and_process(str(file_path), context_aware)
            # Flatten sessions into a single list of messages
            for session in result.get("results", []):
                all_messages.extend(session.get("messages", []))

        # --- Step 2: RAG Analysis ---
        if not all_messages:
            return JSONResponse({"status": "completed", "summary": "No messages found in file.", "clusters": []})
        
        logger.info(f"Passing {len(all_messages)} classified messages to AnalysisService.")
        analysis_report = analysis_service.cluster_and_summarize(all_messages)
        
        return JSONResponse(jsonable_encoder(analysis_report))

    except Exception as e:
        logger.error(f"File analysis failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An internal error occurred: {e}")

    finally:
        if temp_dir.exists():
            shutil.rmtree(temp_dir, ignore_errors=True)
