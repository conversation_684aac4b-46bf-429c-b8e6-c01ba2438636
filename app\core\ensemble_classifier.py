# app/core/ensemble_classifier.py
"""
Classifier that runs multiple models and merges their results into a single,
flat dictionary per message, which is compatible with downstream services.
"""
from __future__ import annotations
from typing import List, Dict, Any
import asyncio
import logging

from app.core.text_classifier import TextClassifier

logger = logging.getLogger(__name__)

class EnsembleClassifier:
    def __init__(self, model_configs: Dict[str, str]) -> None:
        """
        Initializes the ensemble with a dictionary of model names and their paths.
        """
        self.members: List[TextClassifier] = []
        self.model_names: List[str] = []
        for name, path in model_configs.items():
            self.members.append(TextClassifier(path))
            self.model_names.append(name)

    async def load_model(self) -> None:
        """Loads all member models in parallel."""
        await asyncio.gather(*(m.load_model() for m in self.members))

    def _classify_and_merge(
        self, classification_func_name: str, *args, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generic helper to run a classification function on all models and merge results.
        This is the core fix to prevent data structure errors.
        """
        # Get results from each model.
        # all_model_outputs is a list of lists, e.g., [[model1_results], [model2_results]]
        all_model_outputs = [
            getattr(member, classification_func_name)(*args, **kwargs) for member in self.members
        ]

        if not all_model_outputs or not all_model_outputs[0]:
            return []
            
        num_messages = len(all_model_outputs[0])
        final_results = []

        # Iterate through each message/line (by index).
        for i in range(num_messages):
            # Start with the full result from the first model to preserve all metadata
            # like 'text', 'line_number', 'timestamp', etc.
            merged_block = all_model_outputs[0][i].copy()

            # Now, add the specific prediction and confidence for each model
            # using the model's assigned name as a prefix.
            for j, model_name in enumerate(self.model_names):
                if i < len(all_model_outputs[j]):
                    model_prediction = all_model_outputs[j][i]
                    merged_block[f"{model_name}_prediction"] = model_prediction.get("prediction")
                    merged_block[f"{model_name}_confidence"] = model_prediction.get("confidence")
            
            # Clean up the generic keys that are now replaced by model-specific ones
            merged_block.pop("prediction", None)
            merged_block.pop("confidence", None)
            
            final_results.append(merged_block)

        return final_results

    def classify_text(self, text: str, context_aware: bool = False) -> List[Dict[str, Any]]:
        """Classifies each line of raw text with all models."""
        return self._classify_and_merge("classify_text", text, context_aware=context_aware)

    def classify_standard_inputs(
        self, blocks: List[Dict[str, Any]], context_aware: bool = False
    ) -> List[Dict[str, Any]]:
        """Classifies pre-processed message blocks (e.g., from WhatsApp)."""
        return self._classify_and_merge("classify_standard_inputs", blocks, context_aware=context_aware)

    def explain_with_interpret(self, text: str, target_index: int | None = 1) -> List[tuple]:
        """Explanation is only supported for the first model in the ensemble."""
        logger.warning("Explanation is only available for the first model in the ensemble.")
        if not self.members:
            raise RuntimeError("No models in ensemble to explain with.")
        return self.members[0].explain_with_interpret(text, target_index=target_index)

