# app/core/text_classifier.py
"""
Single-model GBERT classifier or a transparent wrapper around an
EnsembleClassifier, depending on the configuration.
"""
from __future__ import annotations
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple

import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from transformers_interpret import SequenceClassificationExplainer

from app import config as _cfg
from app.core.exceptions import ClassificationError

logger = logging.getLogger(__name__)

class TextClassifier:
    """
    A classifier for a single model. Can be instantiated with a specific model path.
    """
    def __init__(self, model_path: str | Path) -> None:
        if not model_path:
            raise ValueError("TextClassifier must be initialized with a model_path.")
        self.model_path = Path(model_path)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model: AutoModelForSequenceClassification | None = None
        self.tokenizer: AutoTokenizer | None = None
        self.labels = ["Non-Relevant", "Relevant"]
        self.threshold = 0.80
        self._explain_cache: dict[Tuple[str, int | None], List[tuple]] = {}

    async def load_model(self) -> None:
        """Loads the model and tokenizer from the specified path."""
        try:
            if not self.model_path.exists():
                raise FileNotFoundError(f"Model path does not exist: {self.model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = (
                AutoModelForSequenceClassification.from_pretrained(self.model_path)
                .to(self.device)
                .eval()
            )
            thr_file = self.model_path / "threshold.txt"
            if thr_file.exists():
                self.threshold = float(thr_file.read_text().strip())
            logger.info("Classifier model [%s] loaded on %s with threshold %.2f", self.model_path.name, self.device, self.threshold)
        except Exception as e:
            logger.error("Failed to load model '%s': %s", self.model_path, e, exc_info=True)
            raise ClassificationError(f"Model initialization failed: {e}") from e

    def classify_text(self, text: str, context_aware: bool = False) -> List[Dict[str, Any]]:
        """Classifies raw text, line by line."""
        if not text or not text.strip(): return []
        lines = [ln.strip() for ln in text.splitlines() if ln.strip()]
        if not lines: return []
        
        if context_aware:
            return self._classify_with_context_window(lines)
        return self._classify_lines(lines)

    def classify_standard_inputs(self, message_blocks: List[Dict[str, Any]], context_aware: bool = False) -> List[Dict[str, Any]]:
        """Adds predictions to a list of pre-parsed message blocks."""
        lines = [(blk.get("context") or blk.get("text", "")) if context_aware else blk.get("text", "") for blk in message_blocks]
        
        # This returns a list of dicts with 'prediction' and 'confidence'
        classified = self._classify_lines(lines)
        
        output: List[Dict[str, Any]] = []
        for i, res in enumerate(classified):
            # Start with a copy of the original block to preserve all metadata
            new_block = message_blocks[i].copy()
            # Add the new prediction and confidence keys
            new_block.update(res)
            # CRITICAL FIX: Ensure the 'text' field always contains the original, non-context message
            new_block["text"] = message_blocks[i].get("text", "")
            output.append(new_block)
        return output

    def explain_with_interpret(self, text: str, target_index: int | None = 1) -> List[tuple]:
        """Generates token attributions for a prediction."""
        if self.model is None or self.tokenizer is None: raise RuntimeError("Model not loaded")
        key = (text, target_index)
        if key in self._explain_cache: return self._explain_cache[key]
        explainer = SequenceClassificationExplainer(self.model, self.tokenizer)
        attributions = explainer(text, index=target_index)
        self._explain_cache[key] = attributions
        return attributions

    def _classify_lines(self, lines: List[str]) -> List[Dict[str, Any]]:
        """Internal helper to run batch classification."""
        results: List[Dict[str, Any]] = []
        for i, line in enumerate(lines):
            if not line or not line.strip():
                results.append({"prediction": "Non-Relevant", "confidence": 1.0, "input_snippet": "", "text": "", "line_number": i + 1})
                continue

            inputs = self.tokenizer(line, truncation=True, padding=True, return_tensors="pt", max_length=_cfg.MAX_SEQUENCE_LENGTH).to(self.device)
            with torch.no_grad():
                logits = self.model(**inputs).logits
            probs = torch.softmax(logits, dim=1)[0]
            pred_idx = torch.argmax(probs).item()
            results.append({
                "input_snippet": line,
                "text": line,
                "line_number": i + 1,
                "prediction": self.labels[pred_idx],
                "confidence": round(float(probs[pred_idx]), 4),
            })
        return results

    def _classify_with_context_window(self, lines: List[str]) -> List[Dict[str, Any]]:
        """Internal helper to create context chunks and classify them."""
        chunks = [f"{lines[i - 1] if i > 0 else ''} [SEP] {lines[i]} [SEP] {lines[i + 1] if i + 1 < len(lines) else ''}".strip() for i in range(len(lines))]
        classified_chunks = self._classify_lines(chunks)
        
        # Overwrite the 'text' field with the original line, not the context chunk.
        for i, res in enumerate(classified_chunks):
            res["text"] = lines[i]
        return classified_chunks

# --- Module-level Façade ---
from app.core.ensemble_classifier import EnsembleClassifier

model_configs = getattr(_cfg, "MODEL_PATHS", {})

if len(model_configs) > 1:
    classifier = EnsembleClassifier(model_configs)
    logger.info(f"Initialized EnsembleClassifier with {len(model_configs)} models.")
else:
    single_model_path = list(model_configs.values())[0] if model_configs else ""
    if not single_model_path:
        raise ValueError("No model path configured. Please set MODEL_PATHS in config.py")
    classifier = TextClassifier(single_model_path)
    logger.info("Initialized TextClassifier in single-model mode.")

async def load_model() -> None:
    """FastAPI startup hook."""
    await classifier.load_model()
