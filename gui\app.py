import streamlit as st
import requests, pandas as pd, re
from typing import Dict, Any, List, Tuple
import plotly.express as px
import io

API_URL = "http://localhost:8500/api"
st.set_page_config(page_title="Forensic Text Classifier", layout="wide")
st.title("🔍 Forensic Text Classification Tool")

# ───────────── Sidebar ─────────────
with st.sidebar:
    st.header("Settings")
    context_aware = st.checkbox("Enable Context-Aware Scan", value=False)
    explain = st.checkbox("Explain prediction (transformers-interpret)", value=False)
    st.divider()
    st.page_link("pages/00_Help.py", label="ℹ️ Help & Supported Files")

SEP_RE = re.compile(r"\s*\[SEP\]\s*")
def _pretty(text: str) -> str:
    return " • ".join(t.strip() for t in SEP_RE.split(text) if t.strip())

def show_folder_kpis(text_files, structured_files):
    totals = {
        "Files": len(text_files) + len(structured_files),
        "Messages": (sum(f.get("total", 0) for f in text_files) +
                     sum(f.get("stats", {}).get("messages", 0) for f in structured_files)),
        "Relevant messages": (sum(f.get("relevant_found", 0) for f in text_files) +
                              sum(f.get("stats", {}).get("relevant_messages", 0) for f in structured_files)),
    }
    for col, (k, v) in zip(st.columns(len(totals)), totals.items()):
        col.metric(k, v)

def make_overview_dataframe(text_files, structured_files) -> pd.DataFrame:
    rows = []
    idx = 1
    for f in text_files:
        rows.append({"id": idx, "file": f["file"], "type": "txt", "sessions": 0,
                     "messages": f["total"], "relevant": f["relevant_found"],
                     "hit_rate": round(f["relevant_found"] / max(f["total"], 1), 3)})
        idx += 1
    for f in structured_files:
        stx = f.get("stats", {})
        rows.append({"id": idx, "file": f["file"], "type": f.get("platform", "structured"),
                     "sessions": stx.get("sessions", 0), "messages": stx.get("messages", 0),
                     "relevant": stx.get("relevant_messages", 0),
                     "hit_rate": round(stx.get("relevant_messages", 0) / max(stx.get("messages", 1), 1), 3)})
        idx += 1
    df = pd.DataFrame(rows).sort_values("relevant", ascending=False)
    df["short_file"] = df["file"].apply(lambda x: x[:15] + "…" if len(x) > 15 else x)
    return df

def _make_message_df(res: Dict[str, Any], pure_txt_mode: bool = False) -> pd.DataFrame:
    """
    Flatten result into message dataframe.
    If pure_txt_mode: Always show original (non-merged) lines for .txt files,
    ignoring session/context windows (prevents [SEP] merged lines in table).
    """
    flat = []
    if res.get("results"):
        # Detect pure txt mode by lack of session "messages"
        if pure_txt_mode and isinstance(res["results"], list) and res["results"] and isinstance(res["results"][0], dict):
            if "input_snippet" in res["results"][0]:
                # Each row is a line, just return as-is
                flat = res["results"]
            else:
                for row in res["results"]:
                    if isinstance(row, dict) and "messages" in row:
                        flat.extend(row.get("messages", []))
        else:
            # Standard: flatten sessionized if any, else just use the results
            if isinstance(res["results"][0], dict) and "messages" in res["results"][0]:
                for sess in res["results"]:
                    flat.extend(sess.get("messages", []))
            else:
                flat.extend(res["results"])
    return pd.DataFrame(flat)

def _style_relevant(df: pd.DataFrame) -> object:
    """Highlight rows where prediction == 'Relevant' in red, works for DataFrame and Styler."""
    def color_row(row):
        return ["background-color:#ffcccc" if str(row.get("prediction", "")).lower() == "relevant" else "" for _ in row]
    return df.style.apply(color_row, axis=1)

def style_overview_table(df: pd.DataFrame) -> object:
    """Highlight overview table rows with any relevant>0 in red (for folder view)."""
    def color_row(row):
        return ["background-color:#ffcccc" if row.get("relevant", 0) > 0 else "" for _ in row]
    return df.style.apply(color_row, axis=1)

def show_summary(stats: Dict[str, Any]):
    if not stats: return
    st.subheader("📄 Summary")
    for col, (k, v) in zip(st.columns(len(stats)), stats.items()):
        col.metric(k.replace("_", " ").title(), v)


def show_structured_file(res: Dict[str, Any], fname: str, highlight_relevant: bool = True):
    show_summary(res.get("stats", {}))
    # --- If CSV string exists, read and display it as a DataFrame ---
    if res.get("csv_string"):
        df = pd.read_csv(io.StringIO(res["csv_string"]))
        if not df.empty:
            st.subheader("Messages")
            # Highlight relevant in red
            def style_relevant(row):
                return ["background-color:#ffcccc" if str(row.get("prediction", "")).lower() == "relevant" else "" for _ in row]
            st.dataframe(df.style.apply(style_relevant, axis=1), use_container_width=True)
    else:
        st.info("No messages found in this file.")


def show_text_file(res: Dict[str, Any], fname: str, highlight_relevant: bool = True):
    # pure_txt_mode: Always show original lines for .txt, never context [SEP] merges
    df = _make_message_df(res, pure_txt_mode=True)
    show_summary(res.get("stats", {}))
    if not df.empty:
        st.subheader("Messages")
        st.dataframe(_style_relevant(df) if highlight_relevant else df, use_container_width=True)

# =========== UI LOGIC =============

choice = st.radio("Choose input method:", ["Raw Text", "Upload File", "Folder Path"])

# ---------- RAW TEXT ----------
if choice == "Raw Text":
    txt = st.text_area("Enter German text to classify", height=200)
    if st.button("Classify Text") and txt.strip():
        with st.spinner("Classifying…"):
            js = requests.post(f"{API_URL}/classify-text",
                               data={"text": txt, "context_aware": context_aware, "explain": explain}).json()
        results = js.get("results", [])
        if results:
            df = pd.DataFrame([{
                "Text": _pretty(r['text']),
                "Prediction": r["prediction"],
                "Confidence": r.get("confidence", None)
            } for r in results])

            def style_relevant(row):
                return ["background-color:#ffcccc" if row.Prediction.lower() == "relevant" else "" for _ in row]

            st.dataframe(df.style.apply(style_relevant, axis=1), use_container_width=True)

            # Explanations with positive contributions in red, ## removed
            def highlight_positive(val):
                try:
                    return "background-color:#ffcccc" if float(val) > 0 else ""
                except Exception:
                    return ""
            def clean_token(token):
                return token.replace('##', '')

            if explain:
                for i, r in enumerate(results):
                    if r.get("explanation"):
                        st.caption(f"Token contribution for line {i+1}:")
                        df_exp = pd.DataFrame(r["explanation"], columns=["token", "score"])
                        df_exp["token"] = df_exp["token"].apply(clean_token)
                        st.dataframe(df_exp.style.applymap(highlight_positive, subset=["score"]), use_container_width=True)



# ---------- FILE UPLOAD ----------
elif choice == "Upload File":
    up = st.file_uploader("Upload .txt/.xlsx or WhatsApp DB", type=["txt", "xlsx", "db", "sqlite"])
    if up and st.button("Classify File / DB"):
        with st.spinner("Processing…"):
            res = requests.post(f"{API_URL}/upload-file",
                               files={"file": up},
                               data={"context_aware": context_aware, "session_gap_minutes": 30,
                                     "context_window_size": 3, "explain": explain}).json()
        st.success("Done")
        # If file is txt, force highlight_relevant and show only raw lines (never merged [SEP])
        is_txt = (res.get("file_type") == "txt")
        (show_text_file if is_txt else show_structured_file)(
            res, res.get("filename_prefix", "result"), highlight_relevant=True
        )

# ---------- FOLDER PATH ----------
else:
    folder = st.text_input("Enter folder path (e.g. C:/evidence)")
    if st.button("Classify Folder") and folder.strip():
        with st.spinner("Scanning…"):
            res = requests.post(f"{API_URL}/classify-folder",
                                data={"folder_path": folder.strip(), "context_aware": context_aware,
                                      "session_gap_minutes": 30, "context_window_size": 3,
                                      "explain": explain}).json()
            # Store results in session for persistent view switching
            st.session_state.folder_res = res
            st.session_state.view_mode = "table"

    # Only show UI if a folder has been scanned
    res = st.session_state.get("folder_res")
    if res:
        text_files = res.get("text_files", [])
        structured_files = res.get("structured_files", [])
        show_folder_kpis(text_files, structured_files)
        df = make_overview_dataframe(text_files, structured_files)
        outs = res.get("file_outputs", {})
        overview_df = df.set_index("id")

        if "view_mode" not in st.session_state:
            st.session_state.view_mode = "table"

        # === Overview Table ===
        if st.session_state.view_mode == "table":
            st.markdown("### 🗂️ Overview Table")
            st.dataframe(
                style_overview_table(df[["id", "file", "type", "messages", "relevant", "hit_rate"]]),
                use_container_width=True
            )
            if st.button("Show Overview Pie"):
                st.session_state.view_mode = "pie"
                st.rerun()

            # # Show file details as expanders under the table
            # for sec, title in [("structured_files", "Structured Files"), ("text_files", "Text Files")]:
            #     items = res.get(sec, [])
            #     if not items: continue
            #     st.subheader(title)
            #     for it in items:
            #         with st.expander(it["file"]):
            #             (show_structured_file if sec == "structured_files" else show_text_file)(
            #                 outs.get(it["file"], {}), it["file"])

        # === Pie Mode ===
        elif st.session_state.view_mode == "pie":
            pie_df = df[["id", "relevant"]].copy()
            pie_df = pie_df[pie_df["relevant"] > 0]
            st.markdown("### 🥧 Overview Pie")
            if not pie_df.empty:
                fig = px.pie(
                    pie_df, names="id", values="relevant",
                    title="Relevant messages per file id",
                    hole=0.45
                )
                fig.update_traces(
                    textposition='inside',
                    textinfo='percent+label',
                    insidetextfont=dict(color='white', size=18),
                    hovertemplate="ID %{label}<br>Relevant: %{value}<extra></extra>"
                )
                fig.update_layout(height=420, margin=dict(l=10, r=10, t=50, b=10), showlegend=True)
                st.plotly_chart(fig, use_container_width=True, config={"staticPlot": False})
                st.caption("Choose a file ID below to view details.")

                ids = pie_df["id"].tolist()
                selected_id = st.selectbox("Select overview id to view details:", ids, key="pieid")
                if selected_id is not None:
                    row = overview_df.loc[selected_id]
                    file_name = row["file"]
                    section = None
                    for sec in ["structured_files", "text_files"]:
                        if any(x["file"] == file_name for x in res.get(sec, [])):
                            section = sec
                            break
                    st.markdown(f"### File details for id **{selected_id}** — `{file_name}`")
                    file_res = outs.get(file_name, {})
                    (show_structured_file if section == "structured_files" else show_text_file)(
                        file_res, file_name, highlight_relevant=True)
            else:
                st.info("No relevant messages to show in pie.")

            # Back button below pie
            if st.button("Back to Overview Table"):
                st.session_state.view_mode = "table"
                st.rerun()

        for e in res.get("errors", []): st.error(e)
