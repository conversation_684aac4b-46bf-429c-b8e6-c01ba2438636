import re
from typing import Dict, Any, List

import pandas as pd
import plotly.express as px
import requests
import streamlit as st

# --- Page and API Configuration ---
st.set_page_config(
    page_title="Forensic Text Classifier",
    layout="wide",
    initial_sidebar_state="expanded"
)
API_URL = "http://localhost:8500/api"
RELEVANT_COLOR = "#990000" # Dark red for better contrast

if "active_tab" not in st.session_state:
    st.session_state.active_tab = "Raw Text"
if "results_data" not in st.session_state:
    st.session_state.results_data = {}
if "context_aware" not in st.session_state:
    st.session_state.context_aware = False
if "explain" not in st.session_state:
    st.session_state.explain = False
if "summarize" not in st.session_state:
    st.session_state.summarize = False

st.session_state.context_aware = st.sidebar.checkbox(
    "Enable Context-Aware Scan",
    value=st.session_state.context_aware,
    help="Sends surrounding messages as context to the model. Slower."
)
st.session_state.explain = st.sidebar.checkbox(
    "Explain Prediction",
    value=st.session_state.explain,
    help="Shows token contributions. Only works for the first model in the ensemble."
)
st.session_state.summarize = st.sidebar.checkbox(
    "Show Summary (RAG Report)",
    value=st.session_state.summarize,
    help="Generates and displays a German-language forensic summary of all detected relevant topics (RAG/LeoLM). Slower, but more insightful."
)
st.sidebar.page_link("app.py", label="🏠 Home")

# --- Table & Summary Helpers ---

def create_results_dataframe(records: List[Dict[str, Any]]) -> pd.DataFrame:
    if not records:
        return pd.DataFrame()

    processed_rows = []
    model_names = sorted(list(set(
        [k.replace("_prediction", "").replace("_confidence", "")
         for k in records[0].keys() if k.endswith(("_prediction", "_confidence"))]
    )))

    for i, record in enumerate(records):
        row = {
            "ID": i + 1,
            "Text": record.get("text", record.get("input_snippet", "")),
        }
        for model in model_names:
            row[f"{model} Prediction"] = record.get(f"{model}_prediction")
            row[f"{model} Confidence"] = record.get(f"{model}_confidence")
        processed_rows.append(row)

    return pd.DataFrame(processed_rows)

def style_dataframe(df: pd.DataFrame) -> 'pd.io.formats.style.Styler':
    if df.empty:
        return df.style

    pred_cols = [col for col in df.columns if "Prediction" in col]
    if not pred_cols:
        return df.style

    def highlight_row(row):
        is_relevant = any(str(val).lower() == "relevant" for val in row[pred_cols])
        return [f"background-color: {RELEVANT_COLOR}; color: white;" if is_relevant else "" for _ in row]

    styler = df.style.apply(highlight_row, axis=1)
    conf_cols = [col for col in df.columns if "Confidence" in col]
    styler = styler.format({col: "{:.4f}" for col in conf_cols})
    return styler

def display_results_table(records: List[Dict[str, Any]], title: str):
    st.subheader(title)
    if not records:
        st.info("No results to display.")
        return

    df = create_results_dataframe(records)
    styled_df = style_dataframe(df)
    st.dataframe(styled_df, use_container_width=True, hide_index=True)

    if st.session_state.explain and records and "explanation" in records[0]:
        st.subheader("Prediction Explanations (First Model Only)")
        for i, record in enumerate(records):
            if "explanation" in record and record["explanation"]:
                with st.expander(f"ID {i+1}: '{record.get('text', '')[:60]}...'"):
                    exp_df = pd.DataFrame(record["explanation"], columns=["Token", "Contribution Score"])
                    st.dataframe(exp_df, use_container_width=True, hide_index=True)

def display_rag_report(rag, label="Summary (RAG Report)", chunk_details=False):
    if not rag or rag.get("clusters") is None:
        st.info("No summary (RAG) report available for this input.")
        return
    st.markdown(f"### {label}")
    st.success(rag.get("summary", ""))
    for c in rag["clusters"]:
        color = "#F6DDCC"
        st.markdown(
            f"""
            <div class="file-card">
                <span class="expander-header">{c.get('cluster_id','Thema')} — <b>{c.get('message_count',0)} Beweismeldungen</b></span>
                <div class="summary-highlight">{c.get('summary', '')}</div>
                <details>
                <summary style="margin-top:0.5em;">Schlüsselbeweismeldungen anzeigen</summary>
                <ul>
                    {''.join(f"<li>{m.get('text','')}</li>" for m in c.get('messages', [])[:8])}
                    {"<li>...weitere Beweismeldungen</li>" if len(c.get('messages', [])) > 8 else ""}
                </ul>
                </details>
            </div>
            """, unsafe_allow_html=True)
        if chunk_details and c.get("chunk_summaries"):
            with st.expander("Details zu Chunks"):
                for idx, chunk_sum in enumerate(c["chunk_summaries"], 1):
                    st.markdown(f"- **Chunk {idx}:** {chunk_sum}")
    st.divider()

# --- Main UI Handlers ---

def handle_raw_text():
    st.subheader("Classify Raw Text")
    txt = st.text_area("Enter German text to classify (one message per line)", height=250, key="raw_text_area")
    
    if st.button("Classify Text", key="raw_text_button"):
        with st.spinner("Classifying..."):
            try:
                payload = {
                    "text": txt,
                    "context_aware": st.session_state.context_aware,
                    "explain": st.session_state.explain,
                    "summarize": st.session_state.summarize,
                }
                res = requests.post(f"{API_URL}/classify-text", data=payload, timeout=None).json()
                st.session_state.results_data = res
            except requests.RequestException as e:
                st.error(f"API Connection Error: {e}")
                st.session_state.results_data = {}

    if st.session_state.results_data:
        display_results_table(st.session_state.results_data.get("results", []), title="Classification Results")
        rag = st.session_state.results_data.get("rag_report")
        if rag:
            display_rag_report(rag, label="RAG Summary (German)", chunk_details=True)

def handle_file_upload():
    st.subheader("Classify a Single File")
    up = st.file_uploader("Upload .txt, .xlsx, .csv, .db, or .sqlite file", type=["txt", "xlsx", "csv", "db", "sqlite"], key="file_uploader")
    
    if st.button("Classify File", key="file_upload_button"):
        if up is None:
            st.warning("Please upload a file first.")
            return
        with st.spinner("Processing and classifying file..."):
            try:
                files = {"file": (up.name, up.getvalue(), up.type)}
                data = {
                    "context_aware": st.session_state.context_aware,
                    "explain": st.session_state.explain,
                    "summarize": st.session_state.summarize,
                }
                res = requests.post(f"{API_URL}/upload-file", files=files, data=data, timeout=None).json()
                st.session_state.results_data = res
                st.success("File processing complete.")
            except requests.RequestException as e:
                st.error(f"API Connection Error: {e}")
                st.session_state.results_data = {}
            except Exception as e:
                st.error(f"An error occurred during file processing: {e}")
                st.session_state.results_data = {}

    if st.session_state.results_data:
        stats = st.session_state.results_data.get("stats")
        if stats:
            st.subheader("File Summary")
            cols = st.columns(len(stats))
            for i, (k, v) in enumerate(stats.items()):
                cols[i].metric(k.replace("_", " ").title(), v)

        records = []
        if st.session_state.results_data.get("file_type") == "structured":
            for sess in st.session_state.results_data.get("results", []):
                records.extend(sess.get("messages", []))
        else:
            records = st.session_state.results_data.get("results", [])
        
        display_results_table(records, title="File Content Results")

        rag = st.session_state.results_data.get("rag_report")
        if rag:
            display_rag_report(rag, label="RAG Summary (German)", chunk_details=True)

def handle_folder_path():
    st.subheader("Classify a Folder and its Subfolders")
    folder = st.text_input("Enter the full path to a folder on the server", key="folder_path_input")
    
    if st.button("Analyze Folder", key="folder_path_button"):
        if not folder:
            st.warning("Please enter a folder path.")
            return
        with st.spinner("Scanning folder and subfolders... This may take a while."):
            try:
                data = {
                    "folder_path": folder.strip(),
                    "context_aware": st.session_state.context_aware,
                    "explain": st.session_state.explain,
                    "summarize": st.session_state.summarize,
                }
                res = requests.post(f"{API_URL}/classify-folder", data=data, timeout=None).json()
                st.session_state.results_data = res
                st.success("Folder scan complete.")
            except requests.RequestException as e:
                st.error(f"API Connection Error: {e}")
                st.session_state.results_data = {}

    if st.session_state.results_data:
        st.divider()
        file_outputs = st.session_state.results_data.get("file_outputs", {})
        if not file_outputs:
            st.info("No supported files were found or processed in the folder.")
            return

        overview_tab, details_tab = st.tabs(["📊 Overview Dashboard", "📄 Detailed Results"])

        overview_data = []
        for fname, foutput in file_outputs.items():
            stats = foutput.get('stats', {})
            messages = stats.get('messages', 0)
            relevant = stats.get('relevant_messages', 0)
            overview_data.append({
                "File": fname,
                "Type": foutput.get("file_type", "N/A"),
                "Messages": messages,
                "Relevant": relevant,
                "Hit Rate": (relevant / messages) if messages > 0 else 0
            })
        overview_df = pd.DataFrame(overview_data)
        
        with overview_tab:
            st.subheader("📈 Folder Scan Overview")
            total_files = len(overview_df)
            total_messages = overview_df["Messages"].sum()
            total_relevant = overview_df["Relevant"].sum()
            kpi_cols = st.columns(3)
            kpi_cols[0].metric("Total Files Processed", f"{total_files:,}")
            kpi_cols[1].metric("Total Messages Scanned", f"{total_messages:,}")
            kpi_cols[2].metric("Total Relevant Messages", f"{total_relevant:,}")

            st.dataframe(
                overview_df.style.format({"Hit Rate": "{:.1%}"}).background_gradient(cmap='Reds', subset=['Hit Rate']),
                use_container_width=True,
                hide_index=True
            )
            chart_df = overview_df[overview_df["Relevant"] > 0].copy()
            if not chart_df.empty:
                st.subheader("Distribution of Relevant Messages")
                if len(chart_df) > 10:
                    chart_df = chart_df.sort_values("Relevant", ascending=False)
                    other_sum = chart_df.iloc[9:]["Relevant"].sum()
                    chart_df = chart_df.iloc[:9]
                    other_row = pd.DataFrame([{"File": "Other Files", "Relevant": other_sum}])
                    chart_df = pd.concat([chart_df, other_row], ignore_index=True)
                fig = px.pie(chart_df, names="File", values="Relevant", title="Relevant Messages per File", hole=0.4)
                fig.update_traces(textposition='inside', textinfo='percent+label', insidetextfont=dict(color='white'))
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No relevant messages found to display in chart.")

            # Show folder-level RAG summary
            rag = st.session_state.results_data.get("global_rag_report")
            if rag:
                display_rag_report(rag, label="Folder-Wide RAG Summary (German)", chunk_details=True)

        with details_tab:
            st.subheader("📄 Detailed File-by-File Results")
            ctrl_cols = st.columns([0.7, 0.3])
            search_query = ctrl_cols[0].text_input("Search for a file...")
            sort_by = ctrl_cols[1].selectbox("Sort by", ["Relevance", "Messages", "File Name"])
            filtered_files = {k: v for k, v in file_outputs.items() if search_query.lower() in k.lower()}
            if sort_by == "Relevance":
                sorted_files = sorted(filtered_files.items(), key=lambda item: item[1].get('stats', {}).get('relevant_messages', 0), reverse=True)
            elif sort_by == "Messages":
                sorted_files = sorted(filtered_files.items(), key=lambda item: item[1].get('stats', {}).get('messages', 0), reverse=True)
            else:
                sorted_files = sorted(filtered_files.items())

            for fname, foutput in sorted_files:
                stats = foutput.get('stats', {})
                messages = stats.get('messages', 0)
                relevant = stats.get('relevant_messages', 0)
                with st.expander(f"**{fname}** | Messages: {messages} | Relevant (any model): {relevant}"):
                    records = []
                    results_data = foutput.get("results", [])
                    if results_data:
                        if foutput.get("file_type") == "structured":
                            for sess in results_data:
                                records.extend(sess.get("messages", []))
                        else:
                            records = results_data
                    display_results_table(records, title="")
                    rag = foutput.get("rag_report")
                    if rag:
                        display_rag_report(rag, label=f"RAG Summary for {fname}", chunk_details=True)

# ========= Main App Execution =========
st.title("🔍 Forensic Text Classification Tool")

handlers = {
    "Raw Text": handle_raw_text,
    "Upload File": handle_file_upload,
    "Folder Path": handle_folder_path,
}
choice = st.radio("Choose input method:", handlers.keys(), horizontal=True, key="input_choice")

if st.session_state.active_tab != choice:
    st.session_state.active_tab = choice
    st.session_state.results_data = {}

handlers[choice]()
