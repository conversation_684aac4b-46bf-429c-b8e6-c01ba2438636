# app/routes/file_upload.py

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder           
from pathlib import Path
import tempfile
import shutil
import numpy as np
import pandas as pd

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.services.analysis_service import analysis_service 
from app.utils.logger import logger
from app.config import DEFAULT_SESSION_GAP, DEFAULT_CONTEXT_WINDOW
from app.utils.conversion import to_builtin_type

router = APIRouter()

@router.post("/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
    explain: bool = Form(False),
    summarize: bool = Form(False),   
):
    """
    Handles the upload and classification of a single file.
    It distinguishes between plain text files and structured files.
    Optionally runs a RAG-based summary if summarize=True.
    """
    temp_dir = Path(tempfile.mkdtemp())
    try:
        file_path = temp_dir / file.filename
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)

        # --- Plain .txt files ---
        if file_path.suffix.lower() == ".txt":
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
            
            results = classifier.classify_text(content, context_aware=context_aware)
            
            if explain:
                for r in results:
                    r["explanation"] = classifier.explain_with_interpret(r["text"])

            output = {
                "status": "completed",
                "file_type": "txt",
                "results": results,
            }

            # RAG summary for .txt files
            if summarize and results:
                rag_report = analysis_service.cluster_and_summarize(results)
                output["rag_report"] = rag_report

            return JSONResponse(jsonable_encoder(to_builtin_type(output)))

        # --- Structured files (.xlsx, .db, etc.) ---
        processor = BaseProcessor(
            session_gap_minutes=session_gap_minutes,
            context_window_size=context_window_size,
        )
        result = processor.detect_and_process(str(file_path), context_aware=context_aware)

        output = {
            "status": "completed",
            "file_type": "structured",
            "platform": result.get("platform", "unknown"),
            "stats": result.get("stats", {}),
            "results": result.get("results", []), # Results are already sessionized
        }

        # Flatten all classified messages (from all sessions) for RAG
        file_results = result.get("results", [])
        flat_results = []
        if file_results and isinstance(file_results, list):
            if isinstance(file_results[0], dict) and "messages" in file_results[0]:
                for sess in file_results:
                    flat_results.extend(sess.get("messages", []))
            else:
                flat_results = file_results

        # RAG summary for structured files
        if summarize and flat_results:
            rag_report = analysis_service.cluster_and_summarize(flat_results)
            output["rag_report"] = rag_report

        return JSONResponse(jsonable_encoder(to_builtin_type(output)))

    except Exception as e:
        logger.error(f"File upload failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An internal error occurred: {e}")

    finally:
        if temp_dir.exists():
            shutil.rmtree(temp_dir, ignore_errors=True)
