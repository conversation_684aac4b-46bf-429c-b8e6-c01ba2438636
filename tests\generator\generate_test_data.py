"""
Generate a high-volume, duplicate-free test-data corpus for the
Forensic Text Classification API.

Creates:
  tests/data/
      ├─ raw_text/mixed_chat.txt
      ├─ whatsapp_exports/{group_chat.csv, group_chat.xlsx, single_chat.ods}
      ├─ databases/msgstore.db
      └─ folder_tree/…

Column names & DB schema match the samples you supplied.
"""

from __future__ import annotations
import random, pathlib, sqlite3, datetime as dt, re
from typing import List, Set

import pandas as pd
from faker import Faker


BASE          = pathlib.Path(__file__).resolve().parents[1] / "data"
N_MESSAGES    = 50_000           
CLASS_SPLIT   = dict(csam=0.015, drugs=0.05, hate=0.05, fraud=0.05, benign=0.835)
GROUP_NAMES   = ["Geheime Gruppe", "Team X", "Dark Market", "Silvester 2023🍾"]
faker         = Faker("de_DE")
random.seed(42)


def berlin_ts() -> dt.datetime:
    """Random timestamp in 2023-2024, Europe/Berlin."""
    return faker.date_time_between_dates(
        dt.datetime(2023, 1, 1), dt.datetime(2024, 12, 31),
        tzinfo=dt.timezone(dt.timedelta(hours=2))  # fixed UTC+02
    )

def ts_str(ts: dt.datetime) -> str:
    return ts.strftime("%d.%m.%Y %H:%M:%S UTC+02:00 (Netzwerk)")

def sloppy(text: str) -> str:
    """
    Inject typos, duplicate chars, misplaced emoji.  Roughly 30 % of words
    get mutated; good for realism, still readable.
    """
    out: List[str] = []
    for w in text.split():
        if random.random() < 0.3:
            # duplicate a character
            if len(w) > 2 and random.random() < 0.5:
                i = random.randrange(1, len(w) - 1)
                w = w[:i] + w[i] + w[i:]
            # drop a vowel
            elif re.search(r"[aeiouäöü]", w, re.I):
                w = re.sub(r"[aeiouäöü]", "", w, count=1, flags=re.I)
            # shuffle emoji to end
            if re.search(r"[^\w\s]", w) and random.random() < 0.5:
                w = re.sub(r"[^\w\s]", "", w) + random.choice(["😂", "😡", "🔥", "🎒"])
        out.append(w)
    return " ".join(out)

def csam_placeholder() -> str:
    return "[CSAM_SHA256_" + faker.sha256(raw_output=False)[:10] + "]"

def msisdn() -> str:
    """12-digit phone string."""
    return faker.msisdn()[:12]


BENIGN_POOL  = [
    "😂 Hast du das Spiel gestern gesehen?",
    "brb, kurz Kaffee ☕ holen",
    "lol, keine Ahnung",
    "ok, c u later",
    "machst du heute was?",
    "Bin gleich da!",
    "k, thx",
    "Tach, Vollmond 🌕 heut",
    faker.catch_phrase(),
    faker.sentence(nb_words=8),
]

SUBSTANCES   = ["Gras", "MDMA", "Koks", "LSD", "Pillen"]

def illegal_text(cat: str) -> str:
    if cat == "csam":
        return csam_placeholder()
    if cat == "drugs":
        qty = random.randint(1, 20)
        sub = random.choice(SUBSTANCES)
        return random.choice([
            f"Hab noch {qty}g {sub} übrig, meld dich.",
            f"Lieferung {sub} kommt um Mitternacht, sei bereit",
            f"Brauch 2g {sub} asap, kannst du los?",
            f"{sub} ist 🔥 diesmal, 90 % rein!",
        ])
    if cat == "hate":
        target = random.choice(["Ausländer", "Flüchtlinge", "die Politiker"])
        return random.choice([
            f"Diese {target} sollen zurück in ihr Land!",
            f"Ich hasse alle {target} 😡",
            f"{target} haben hier nix verloren!",
            f"Weg mit dem Pack!",
        ])
    if cat == "fraud":
        card = faker.credit_card_number()
        return random.choice([
            f"CC-Dump: {card}, Limit 5k €",
            "Schick mir die TAN schnell, Bruder.",
            "VPN ready, geh jetzt auf die Bankseite.",
            "Falsche Ausweise angekommen, Qualität top.",
        ])
    raise ValueError(cat)

def synth(cat: str, uid: int) -> dict:
    ts  = berlin_ts()
    dir = "Ausgehend" if random.random() < 0.5 else "Eingehend"
    if cat == "benign":
        msg = random.choice(BENIGN_POOL)
    else:
        msg = illegal_text(cat)
    msg = sloppy(msg)
    return {
        "timestamp_dt": ts,
        "timestamp_str": ts_str(ts),
        "sender": faker.first_name(),
        "message": msg,
        "category": cat,
        "direction": dir,
        "index": uid,
        "unique_id": faker.sha1(raw_output=False),
        "phone": msisdn(),
    }


corpus: List[dict] = []
seen_messages: Set[str] = set()
weights = list(CLASS_SPLIT.values())
cats    = list(CLASS_SPLIT.keys())

uid = 1
while len(corpus) < N_MESSAGES:
    cat = random.choices(cats, weights)[0]
    row = synth(cat, uid)
    if row["message"] in seen_messages:
        continue  # reject duplicate
    seen_messages.add(row["message"])
    corpus.append(row)
    uid += 1

df = pd.DataFrame(corpus).sort_values("timestamp_dt").reset_index(drop=True)


raw_dir = BASE / "raw_text"
raw_dir.mkdir(parents=True, exist_ok=True)
df.sample(4_000, random_state=1)["message"].to_csv(
    raw_dir / "mixed_chat.txt", index=False, header=False
)


def single_chat_df(sample: pd.DataFrame) -> pd.DataFrame:
    return pd.DataFrame(
        {
            "Text": sample["message"],
            "Verknüpfte Anwendung": "WhatsApp",
            "Zeit": sample["timestamp_str"],
            "Richtung": sample["direction"],
            "Speicherort": "Gerät",
            "Index": sample["index"],
            "Eindeutige ID": sample["unique_id"].str[:32],
            "Anbieter": "WhatsApp",
            "Kontakte": "",
            "Anruf-Nummer": sample["phone"],
            "Anhang 1": "",
            "Anhang 1 Datei-Name": "",
        }
    )

wa_dir = BASE / "whatsapp_exports"
wa_dir.mkdir(exist_ok=True)

sample_sc = df.sample(1_500, random_state=2)
sc_df = single_chat_df(sample_sc)
sc_df.to_csv(wa_dir / "group_chat.csv", index=False)
sc_df.to_excel(wa_dir / "group_chat.xlsx", sheet_name="Chat", index=False)
sc_df.iloc[:600].to_excel(
    wa_dir / "single_chat.ods", engine="odf", sheet_name="Chat", index=False
)


def group_chat_df(sample: pd.DataFrame) -> pd.DataFrame:
    return pd.DataFrame(
        {
            "Text": sample["message"],
            "Verknüpfte Anwendung": "WhatsApp",
            "Gruppenname": [random.choice(GROUP_NAMES) for _ in range(len(sample))],
            "Zeit": sample["timestamp_str"],
            "Richtung": sample["direction"],
            "Typ": "Text",
            "Zugehörige ID": [faker.sha1(raw_output=False)[:15] for _ in range(len(sample))],
            "Speicherort": "Gerät",
            "Index": sample["index"],
        }
    )

gc_df = group_chat_df(df.sample(1_200, random_state=3))
gc_df.to_excel(wa_dir / "test_group_realistic.xlsx", sheet_name="Chat", index=False)


db_dir = BASE / "databases"
db_dir.mkdir(exist_ok=True)
db_path = db_dir / "msgstore.db"

conn = sqlite3.connect(db_path)
cur  = conn.cursor()
cur.executescript(
    """
DROP TABLE IF EXISTS chat;
DROP TABLE IF EXISTS message;
CREATE TABLE chat(_id INTEGER PRIMARY KEY AUTOINCREMENT, subject TEXT);
CREATE TABLE message(
    _id INTEGER PRIMARY KEY AUTOINCREMENT,
    chat_row_id INTEGER,
    from_me INTEGER,
    key_id TEXT,
    sender_jid_row_id INTEGER,
    status INTEGER,
    timestamp INTEGER,
    text_data TEXT
);
"""
)
# three chats
for subj in ["Privat", "DunkleEcke", "TeamX"]:
    cur.execute("INSERT INTO chat(subject) VALUES (?)", (subj,))

for r in df.sample(3_500, random_state=4).itertuples():
    cur.execute(
        """
        INSERT INTO message(chat_row_id, from_me, key_id,
                            sender_jid_row_id, status, timestamp, text_data)
        VALUES (?,?,?,?,?,?,?)
        """,
        (
            random.randint(1, 3),
            1 if r.direction == "Ausgehend" else 0,
            r.unique_id[:15],
            0,
            0,
            int(r.timestamp_dt.timestamp() * 1000),
            r.message,
        ),
    )

conn.commit(); conn.close()


tree = BASE / "folder_tree"
(tree / "nested/even_deeper").mkdir(parents=True, exist_ok=True)

df.sample(500, random_state=5)["message"].to_csv(
    tree / "alice_chat.txt", header=False, index=False
)

df.sample(250, random_state=6)[["timestamp_str", "sender", "message"]].to_excel(
    tree / "nested/bob_group.xlsx", index=False
)

deep_db = tree / "nested/even_deeper/charlie_db.db"
c = sqlite3.connect(deep_db)
c.execute("CREATE TABLE messages(ts TEXT, body TEXT)")
for msg in df.sample(200, random_state=7)["message"]:
    c.execute("INSERT INTO messages VALUES (?,?)", (berlin_ts().isoformat(), msg))
c.commit(); c.close()

print("messages created under", BASE)
