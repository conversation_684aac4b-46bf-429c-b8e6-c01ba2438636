"""
Unit tests for utility functions
"""
import pytest
from unittest.mock import Mock, patch, mock_open
import json
from datetime import datetime, timezone
from pathlib import Path
import tempfile
import shutil

from app.utils.output_utils import (
    flatten_sessions, 
    results_to_csv_string, 
    results_to_jsonl_string,
    save_results_to_files,
    create_output_directory
)
from app.utils.logger import logger


class TestOutputUtils:
    """Test cases for output utility functions"""
    
    def test_flatten_sessions_empty(self):
        """Test flattening empty sessions"""
        result = flatten_sessions([])
        assert result == []
    
    def test_flatten_sessions_single_session(self):
        """Test flattening single session"""
        sessions = [
            {
                "session_id": 1,
                "messages": [
                    {"text": "message1", "sender": "user1"},
                    {"text": "message2", "sender": "user2"}
                ]
            }
        ]
        
        result = flatten_sessions(sessions)
        
        assert len(result) == 2
        assert result[0]["text"] == "message1"
        assert result[0]["session_id"] == 1
        assert result[1]["text"] == "message2"
        assert result[1]["session_id"] == 1
    
    def test_flatten_sessions_multiple_sessions(self):
        """Test flattening multiple sessions"""
        sessions = [
            {
                "session_id": 1,
                "messages": [{"text": "msg1", "sender": "user1"}]
            },
            {
                "session_id": 2, 
                "messages": [{"text": "msg2", "sender": "user2"}]
            }
        ]
        
        result = flatten_sessions(sessions)
        
        assert len(result) == 2
        assert result[0]["session_id"] == 1
        assert result[1]["session_id"] == 2
    
    def test_results_to_csv_string_empty(self):
        """Test CSV conversion with empty results"""
        result = results_to_csv_string([])
        assert result.strip() == ""
    
    def test_results_to_csv_string_with_data(self):
        """Test CSV conversion with data"""
        results = [
            {
                "text": "test message",
                "sender": "user1",
                "timestamp": datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
                "prediction": "Relevant",
                "confidence": 0.85
            }
        ]
        
        csv_string = results_to_csv_string(results)
        
        assert "text,sender,timestamp,prediction,confidence" in csv_string
        assert "test message" in csv_string
        assert "user1" in csv_string
        assert "Relevant" in csv_string
    
    def test_results_to_csv_string_datetime_conversion(self):
        """Test CSV conversion handles datetime objects"""
        results = [
            {
                "timestamp": datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
                "text": "test"
            }
        ]
        
        csv_string = results_to_csv_string(results)
        
        # Should convert datetime to string
        assert "2024-01-01" in csv_string
    
    def test_results_to_jsonl_string_empty(self):
        """Test JSONL conversion with empty results"""
        result = results_to_jsonl_string([])
        assert result.strip() == ""
    
    def test_results_to_jsonl_string_with_data(self):
        """Test JSONL conversion with data"""
        results = [
            {"text": "message1", "sender": "user1"},
            {"text": "message2", "sender": "user2"}
        ]
        
        jsonl_string = results_to_jsonl_string(results)
        lines = jsonl_string.strip().split('\n')
        
        assert len(lines) == 2
        
        # Each line should be valid JSON
        json1 = json.loads(lines[0])
        json2 = json.loads(lines[1])
        
        assert json1["text"] == "message1"
        assert json2["text"] == "message2"
    
    def test_results_to_jsonl_string_datetime_conversion(self):
        """Test JSONL conversion handles datetime objects"""
        results = [
            {
                "timestamp": datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
                "text": "test"
            }
        ]
        
        jsonl_string = results_to_jsonl_string(results)
        parsed = json.loads(jsonl_string.strip())
        
        # Should convert datetime to ISO string
        assert "2024-01-01T10:00:00+00:00" in parsed["timestamp"]
    
    def test_create_output_directory(self, temp_dir):
        """Test output directory creation"""
        base_path = temp_dir / "test_output"
        
        result_dir = create_output_directory(str(base_path))
        
        assert result_dir.exists()
        assert result_dir.is_dir()
        assert result_dir.parent == base_path
        # Directory name should contain timestamp
        assert "results_" in result_dir.name
    
    def test_create_output_directory_existing_base(self, temp_dir):
        """Test output directory creation when base exists"""
        base_path = temp_dir / "existing_base"
        base_path.mkdir()
        
        result_dir = create_output_directory(str(base_path))
        
        assert result_dir.exists()
        assert result_dir.parent == base_path
    
    @patch('app.utils.output_utils.Path.write_text')
    def test_save_results_to_files_session_format(self, mock_write_text, temp_dir):
        """Test saving results in session format"""
        sessions = [
            {
                "session_id": 1,
                "messages": [{"text": "msg1", "sender": "user1"}]
            }
        ]
        
        output_dir = temp_dir / "output"
        output_dir.mkdir()
        
        save_results_to_files(sessions, output_dir, format_type="session")
        
        # Should write both CSV and JSONL files
        assert mock_write_text.call_count == 2
    
    @patch('app.utils.output_utils.Path.write_text')
    def test_save_results_to_files_flat_format(self, mock_write_text, temp_dir):
        """Test saving results in flat format"""
        results = [{"text": "msg1", "sender": "user1"}]
        
        output_dir = temp_dir / "output"
        output_dir.mkdir()
        
        save_results_to_files(results, output_dir, format_type="flat")
        
        # Should write both CSV and JSONL files
        assert mock_write_text.call_count == 2
    
    def test_save_results_to_files_empty_data(self, temp_dir):
        """Test saving empty results"""
        output_dir = temp_dir / "output"
        output_dir.mkdir()
        
        # Should not raise exception with empty data
        save_results_to_files([], output_dir)
        
        # Files should still be created (empty)
        csv_file = output_dir / "results.csv"
        jsonl_file = output_dir / "results.jsonl"
        
        # Files might not exist if no data to write, which is acceptable


class TestLogger:
    """Test cases for logger utility"""
    
    def test_logger_exists(self):
        """Test that logger is properly configured"""
        assert logger is not None
        assert hasattr(logger, 'info')
        assert hasattr(logger, 'error')
        assert hasattr(logger, 'warning')
        assert hasattr(logger, 'debug')
    
    def test_logger_methods_callable(self):
        """Test that logger methods are callable"""
        # These should not raise exceptions
        logger.info("Test info message")
        logger.warning("Test warning message")
        logger.error("Test error message")
        logger.debug("Test debug message")
    
    @patch('app.utils.logger.logging.getLogger')
    def test_logger_configuration(self, mock_get_logger):
        """Test logger configuration"""
        # Import logger to trigger configuration
        from app.utils.logger import logger
        
        # Should have called getLogger
        mock_get_logger.assert_called()


class TestConversionUtils:
    """Test cases for conversion utilities"""
    
    def test_to_builtin_type_datetime(self):
        """Test datetime conversion to builtin type"""
        from app.utils.conversion import to_builtin_type
        
        dt = datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        result = to_builtin_type(dt)
        
        assert isinstance(result, str)
        assert "2024-01-01T10:00:00+00:00" == result
    
    def test_to_builtin_type_string(self):
        """Test string conversion (should remain unchanged)"""
        from app.utils.conversion import to_builtin_type
        
        text = "test string"
        result = to_builtin_type(text)
        
        assert result == text
        assert isinstance(result, str)
    
    def test_to_builtin_type_number(self):
        """Test number conversion (should remain unchanged)"""
        from app.utils.conversion import to_builtin_type
        
        # Test int
        num_int = 42
        result_int = to_builtin_type(num_int)
        assert result_int == num_int
        assert isinstance(result_int, int)
        
        # Test float
        num_float = 3.14
        result_float = to_builtin_type(num_float)
        assert result_float == num_float
        assert isinstance(result_float, float)
    
    def test_to_builtin_type_none(self):
        """Test None conversion"""
        from app.utils.conversion import to_builtin_type
        
        result = to_builtin_type(None)
        assert result is None
    
    def test_to_builtin_type_list(self):
        """Test list conversion"""
        from app.utils.conversion import to_builtin_type
        
        test_list = [1, "test", datetime(2024, 1, 1, tzinfo=timezone.utc)]
        result = to_builtin_type(test_list)
        
        assert isinstance(result, list)
        assert len(result) == 3
        assert result[0] == 1
        assert result[1] == "test"
        assert isinstance(result[2], str)  # datetime should be converted
    
    def test_to_builtin_type_dict(self):
        """Test dictionary conversion"""
        from app.utils.conversion import to_builtin_type
        
        test_dict = {
            "number": 42,
            "text": "test",
            "timestamp": datetime(2024, 1, 1, tzinfo=timezone.utc)
        }
        result = to_builtin_type(test_dict)
        
        assert isinstance(result, dict)
        assert result["number"] == 42
        assert result["text"] == "test"
        assert isinstance(result["timestamp"], str)  # datetime should be converted
