"""
Essential unit tests for API routes
"""
import pytest
from unittest.mock import patch
from fastapi.testclient import TestClient
import io

from app.main import app


class TestAPIRoutes:
    """Essential tests for API routes"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)


class TestRawTextRoutes(TestAPIRoutes):
    """Essential tests for raw text routes"""

    @patch('app.routes.raw_text.classifier')
    def test_classify_text_success(self, mock_classifier, client):
        """Test successful text classification"""
        mock_classifier.classify_text.return_value = [
            {"text": "test", "prediction": "Relevant", "confidence": 0.85}
        ]

        response = client.post(
            "/classify-text",
            data={"text": "Test message", "context_aware": False, "explain": False}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "results" in data

    @patch('app.routes.raw_text.classifier')
    def test_classify_text_error(self, mock_classifier, client):
        """Test error handling"""
        mock_classifier.classify_text.side_effect = Exception("Test error")

        response = client.post(
            "/classify-text",
            data={"text": "Test", "context_aware": False, "explain": False}
        )

        assert response.status_code == 500
        data = response.json()
        assert data["status"] == "error"

    @patch('app.routes.raw_text.analysis_service')
    def test_health_check_healthy(self, mock_service, client):
        """Test health check when healthy"""
        mock_service.health_check.return_value = {"status": "healthy"}

        response = client.get("/analysis-service-health")
        assert response.status_code == 200

    @patch('app.routes.raw_text.analysis_service')
    def test_health_check_unhealthy(self, mock_service, client):
        """Test health check when unhealthy"""
        mock_service.health_check.return_value = {"status": "unhealthy"}

        response = client.get("/analysis-service-health")
        assert response.status_code == 500


class TestFileUploadRoutes(TestAPIRoutes):
    """Essential tests for file upload routes"""

    @patch('app.routes.file_upload.classifier')
    def test_upload_txt_file(self, mock_classifier, client):
        """Test .txt file upload"""
        mock_classifier.classify_text.return_value = [
            {"text": "test", "prediction": "Relevant"}
        ]

        file_data = io.BytesIO(b"Test content")
        response = client.post(
            "/upload-file",
            files={"file": ("test.txt", file_data, "text/plain")},
            data={"session_gap_minutes": 10, "context_window_size": 3, "context_aware": False, "explain": False}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"

    def test_upload_file_error(self, client):
        """Test file upload error handling"""
        with patch('app.routes.file_upload.BaseProcessor') as mock_processor:
            mock_processor.return_value.detect_and_process.side_effect = Exception("Error")

            file_data = io.BytesIO(b"test")
            response = client.post(
                "/upload-file",
                files={"file": ("test.xlsx", file_data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")},
                data={"session_gap_minutes": 10, "context_window_size": 3, "context_aware": False, "explain": False}
            )

            assert response.status_code == 500


class TestFolderPathRoutes(TestAPIRoutes):
    """Essential tests for folder path routes"""

    def test_classify_folder_invalid_path(self, client):
        """Test invalid folder path"""
        response = client.post(
            "/classify-folder",
            data={
                "folder_path": "/nonexistent",
                "session_gap_minutes": 10,
                "context_window_size": 3,
                "context_aware": False,
                "explain": False
            }
        )

        assert response.status_code == 400
