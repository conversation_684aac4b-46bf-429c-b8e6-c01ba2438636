{"best_metric": 0.9159663865546218, "best_model_checkpoint": "models/gbert_binary_classifier/checkpoint-372", "epoch": 3.0, "eval_steps": 500, "global_step": 372, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.008064516129032258, "grad_norm": NaN, "learning_rate": 0.0, "loss": 0.6565, "step": 1}, {"epoch": 0.8064516129032258, "grad_norm": 13.482885360717773, "learning_rate": 1.8800000000000002e-06, "loss": 0.6787, "step": 100}, {"epoch": 1.0, "eval_accuracy": 0.5954545454545455, "eval_loss": 0.5937894582748413, "eval_macro_f1": 0.7119741100323624, "eval_precision": 0.5527638190954773, "eval_recall": 1.0, "eval_runtime": 3.2112, "eval_samples_per_second": 68.509, "eval_steps_per_second": 8.719, "step": 124}, {"epoch": 1.6129032258064515, "grad_norm": 60.21281814575195, "learning_rate": 3.86e-06, "loss": 0.5763, "step": 200}, {"epoch": 2.0, "eval_accuracy": 0.8727272727272727, "eval_loss": 0.4305714964866638, "eval_macro_f1": 0.8861788617886179, "eval_precision": 0.8014705882352942, "eval_recall": 0.990909090909091, "eval_runtime": 4.6937, "eval_samples_per_second": 46.871, "eval_steps_per_second": 5.965, "step": 248}, {"epoch": 2.4193548387096775, "grad_norm": 48.51152038574219, "learning_rate": 5.86e-06, "loss": 0.3557, "step": 300}, {"epoch": 3.0, "eval_accuracy": 0.9090909090909091, "eval_loss": 0.3672795295715332, "eval_macro_f1": 0.9159663865546218, "eval_precision": 0.8515625, "eval_recall": 0.990909090909091, "eval_runtime": 7.9669, "eval_samples_per_second": 27.614, "eval_steps_per_second": 3.515, "step": 372}], "logging_steps": 100, "max_steps": 496, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 500, "total_flos": 2767836149084160.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}