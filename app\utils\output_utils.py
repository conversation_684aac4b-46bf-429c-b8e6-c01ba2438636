import json
import csv
from io import StringIO, BytesIO
from datetime import datetime
from typing import List, Dict, Any

def results_to_jsonl_string(results: List[Dict[str, Any]]) -> str:
    """Convert results to a JSONL string."""
    lines = [
        json.dumps(item, ensure_ascii=False, default=str)
        for item in results
    ]
    return "\n".join(lines)

def results_to_csv_string(results: List[Dict[str, Any]]) -> str:
    """Convert results to a CSV string."""
    if not results:
        return ""
    keys = sorted(set().union(*(r.keys() for r in results)))
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=keys, extrasaction='ignore')
    writer.writeheader()
    for row in results:
        row = {k: (v.isoformat() if isinstance(v, datetime) else v) for k, v in row.items()}
        writer.writerow(row)
    return output.getvalue()


def results_to_csv_bytes(results: List[Dict[str, Any]]) -> bytes:
    """Convert results to CSV bytes for download."""
    csv_str = results_to_csv_string(results)
    return csv_str.encode("utf-8")

def results_to_jsonl_bytes(results: List[Dict[str, Any]]) -> bytes:
    """Convert results to JSONL bytes for download."""
    jsonl_str = results_to_jsonl_string(results)
    return jsonl_str.encode("utf-8")

# Example helper for backend:
def flatten_sessions(data: Any) -> List[Dict[str, Any]]:
    flat_rows = []
    if isinstance(data, list):
        for d in data:
            if isinstance(d, dict) and "messages" in d:
                session_id = d.get("session_id", "")
                for msg in d.get("messages", []):
                    row = msg.copy()
                    row["session_id"] = session_id
                    flat_rows.append(row)
            elif isinstance(d, dict):
                flat_rows.append(d)
    return flat_rows

