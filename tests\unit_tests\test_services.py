"""
Essential unit tests for service classes
"""
import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone, timedelta
from pathlib import Path

from app.services.sessionizer import Sessionizer
from app.services.base_processor import BaseProcessor
from app.services.analysis_service import AnalysisService


class TestSessionizer:
    """Essential tests for Sessionizer"""

    def test_init(self):
        """Test initialization"""
        sessionizer = Sessionizer(session_gap=timedelta(minutes=30))
        assert sessionizer.session_gap == timedelta(minutes=30)

    def test_sessionize_empty(self):
        """Test empty input"""
        sessionizer = Sessionizer()
        assert sessionizer.sessionize([]) == []

    def test_sessionize_single_message(self, sample_messages):
        """Test single message creates one session"""
        sessionizer = Sessionizer()
        result = sessionizer.sessionize([sample_messages[0]])

        assert len(result) == 1
        assert result[0]["session_id"] == 1
        assert len(result[0]["messages"]) == 1


class TestBaseProcessor:
    """Essential tests for BaseProcessor"""

    def test_init(self):
        """Test initialization"""
        processor = BaseProcessor(session_gap_minutes=20, context_window_size=5)
        assert processor.session_gap_minutes == 20
        assert processor.context_window_size == 5

    def test_detect_and_process_nonexistent_path(self):
        """Test non-existent path raises error"""
        processor = BaseProcessor()
        with pytest.raises(FileNotFoundError):
            processor.detect_and_process("nonexistent/path")

    def test_detect_and_process_txt_file_rejected(self, sample_text_file):
        """Test .txt files are rejected"""
        processor = BaseProcessor()
        with pytest.raises(ValueError, match=".txt"):
            processor.detect_and_process(str(sample_text_file))





class TestAnalysisService:
    """Essential tests for AnalysisService"""

    def test_init(self):
        """Test initialization"""
        service = AnalysisService()
        assert service.embedding_model is None
        assert service.summarizer is None
        assert service._initialized is False

    @patch('app.services.analysis_service.Path')
    def test_ensure_models_loaded_missing_file(self, mock_path):
        """Test model loading with missing file raises error"""
        mock_path.return_value.exists.return_value = False
        service = AnalysisService()

        with pytest.raises(FileNotFoundError):
            service._ensure_models_loaded()

    def test_health_check_error_handling(self):
        """Test health check handles errors"""
        service = AnalysisService()

        with patch('app.services.analysis_service.Path') as mock_path:
            mock_path.return_value.exists.side_effect = Exception("Test error")
            result = service.health_check()

            assert result["status"] == "unhealthy"
            assert "error" in result

    def test_filter_and_deduplicate_empty(self):
        """Test filtering empty list"""
        service = AnalysisService()
        service.model_names = ["gbert-base"]
        assert service._filter_and_deduplicate([]) == []

    def test_filter_and_deduplicate_relevant_messages(self):
        """Test filtering keeps relevant messages"""
        service = AnalysisService()
        service.model_names = ["gbert-base"]

        messages = [
            {"text": "relevant", "gbert-base_prediction": "Relevant", "gbert-base_confidence": 0.9},
            {"text": "not relevant", "gbert-base_prediction": "Non-Relevant", "gbert-base_confidence": 0.8}
        ]

        result = service._filter_and_deduplicate(messages)
        assert len(result) == 1
        assert result[0]["text"] == "relevant"
