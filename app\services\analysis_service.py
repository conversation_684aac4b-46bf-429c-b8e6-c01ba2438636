# app/services/analysis_service.py

import numpy as np
import faiss
from sklearn.cluster import DBSCAN
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Set
import logging

from llama_cpp import <PERSON>lama 

from app.config import (
    EMBEDDING_MODEL_PATH, 
    LOCAL_LLM_PATH, 
    ANALYSIS_RELEVANCE_THRESHOLD,
    MODEL_PATHS
)

logger = logging.getLogger(__name__)

class AnalysisService:
    def __init__(self):
        logger.info("Initializing AnalysisService...")
        self.model_names = list(MODEL_PATHS.keys())
        try:
            self.embedding_model: SentenceTransformer = SentenceTransformer(EMBEDDING_MODEL_PATH)
            
            logger.info(f"Loading German LeoLM model from: {LOCAL_LLM_PATH}")
            self.summarizer: Llama = Llama(
                model_path=LOCAL_LLM_PATH,
                n_ctx=4096,
                n_gpu_layers=-1,
                verbose=False
            )
            logger.info("AnalysisService models (embedding and LeoLM) loaded successfully.")
        except Exception as e:
            logger.error(f"Failed to load analysis models: {e}", exc_info=True)
            raise

    def cluster_and_summarize(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Performs an advanced RAG pipeline with prompt engineering and detailed logging.
        """
        print("\n" + "="*80)
        print("🚀 STARTING ADVANCED RAG ANALYSIS WITH GERMAN LEO-LM")
        print("="*80)
        
        # Step 1: Filter and Deduplicate Relevant Messages
        relevant_messages = self._filter_and_deduplicate(messages)
        print(f"\n   [Step 1] COMPLETE: Found {len(relevant_messages)} unique relevant messages (key evidence).")

        # --- IMPROVEMENT: More informative early exit ---
        if len(relevant_messages) < 2:
            print("\n" + "!"*80)
            print("   ANALYSIS STOPPED: Not enough relevant messages (minimum 2) found to create topic clusters.")
            print(f"   Consider lowering the ANALYSIS_RELEVANCE_THRESHOLD in config.py (current value: {ANALYSIS_RELEVANCE_THRESHOLD})")
            print("!"*80 + "\n")
            return {"status": "completed", "summary": "Not enough relevant messages to create topic clusters.", "clusters": []}
            
        # Step 2: Create a Vector Database of the ENTIRE file
        all_texts = [msg.get('text', '') for msg in messages]
        all_embeddings = self.embedding_model.encode(all_texts, convert_to_tensor=False, show_progress_bar=False)
        faiss.normalize_L2(all_embeddings)
        index = faiss.IndexFlatIP(all_embeddings.shape[1])
        index.add(all_embeddings)
        print(f"   [Step 2] COMPLETE: Created vector DB of all {len(messages)} messages.")

        # Step 3: Retrieve Context for each Relevant Message
        relevant_texts = [msg.get('text', '') for msg in relevant_messages]
        relevant_embeddings = self.embedding_model.encode(relevant_texts, convert_to_tensor=False, show_progress_bar=False)
        faiss.normalize_L2(relevant_embeddings)
        
        k = 5
        distances, indices = index.search(relevant_embeddings, k)
        print(f"   [Step 3] COMPLETE: Retrieved top {k} context messages for each relevant message.")

        # Step 4: Cluster Relevant Messages Based on their Retrieved Context
        context_embeddings = np.array([np.mean(all_embeddings[i], axis=0) for i in indices])
        faiss.normalize_L2(context_embeddings)
        
        clusters = DBSCAN(eps=0.15, min_samples=2, metric="cosine").fit_predict(context_embeddings)
        unique_clusters = set(clusters)
        num_clusters_found = len(unique_clusters) - (1 if -1 in unique_clusters else 0)
        print(f"   [Step 4] COMPLETE: Clustered evidence into {num_clusters_found} topics.")

        # Step 5: Consolidate and Summarize with Prompt Engineering
        analysis_results = []
        print("\n" + "-"*80 + "\n🧠 SUMMARIZING TOPICS WITH ADVANCED PROMPTS\n" + "-"*80)
        
        for cluster_id in sorted(unique_clusters):
            if cluster_id == -1: continue

            print(f"\n   [Topic Cluster ID: {cluster_id}]")
            cluster_indices = [i for i, cid in enumerate(clusters) if cid == cluster_id]
            key_evidence_messages = [relevant_messages[i] for i in cluster_indices]
            
            context_indices_for_cluster: Set[int] = set()
            for i in cluster_indices:
                context_indices_for_cluster.update(indices[i])
            
            full_context_messages = sorted([messages[i] for i in context_indices_for_cluster], key=lambda x: x.get('timestamp', ''))
            
            prompt = self._build_summary_prompt(key_evidence_messages, full_context_messages)
            
            try:
                response = self.summarizer.create_chat_completion(
                    messages=[
                        {"role": "system", "content": "Du bist ein hochqualifizierter forensischer Analyst des deutschen BKA. Deine Aufgabe ist es, eine kurze, analytische Zusammenfassung potenzieller rechtswidriger Aktivitäten auf der Grundlage der bereitgestellten Chat-Nachrichten zu erstellen. Antworte nur mit der Zusammenfassung."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=300
                )
                summary_text = response['choices'][0]['message']['content'].strip()
                print(f"   > ✅ LeoLM Summary: \"{summary_text}\"")
            except Exception as e:
                logger.error(f"LeoLM summarization failed for cluster {cluster_id}: {e}")
                summary_text = "Zusammenfassung für dieses Thema konnte nicht erstellt werden."
                print(f"   > ❌ LeoLM summarization failed: {e}")

            analysis_results.append({
                "cluster_id": f"Thema {cluster_id + 1}",
                "summary": summary_text,
                "message_count": len(key_evidence_messages),
                "messages": key_evidence_messages,
            })
            
        report = {"status": "completed", "summary": f"{len(analysis_results)} unterschiedliche relevante Themen identifiziert.", "clusters": analysis_results}
        print("\n" + "="*80 + "\n✅ ADVANCED RAG ANALYSIS COMPLETE\n" + "="*80 + "\n")
        return report

    def _build_summary_prompt(self, key_messages: List[Dict], context_messages: List[Dict]) -> str:
        """Constructs a more advanced, instruction-rich prompt for the summarization model."""
        prompt = "Analysieren Sie das folgende Chat-Protokoll. Überprüfen Sie zuerst die SCHLÜSSELBEWEISE und verwenden Sie dann die UMGEBENDE KONVERSATION für den Kontext. Identifizieren und fassen Sie alle gefundenen eindeutigen rechtswidrigen Themen zusammen. Seien Sie prägnant und analytisch.\n\n"
        prompt += "[SCHLÜSSELBEWEISE (Nachrichten, die als relevant eingestuft wurden)]:\n"
        for msg in key_messages:
            prompt += f"- {msg.get('text', '')}\n"
        prompt += "\n[UMGEBENDE KONVERSATION (Für den Kontext)]:\n"
        for msg in context_messages:
            prompt += f"- {msg.get('text', '')}\n"
        prompt += "\n[ANALYTISCHE ZUSAMMENFASSUNG DER THEMEN]:"
        return prompt

    def _filter_and_deduplicate(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filters for relevant messages and ensures each unique message appears only once.
        Includes detailed print statements for debugging.
        """
        print("   [Step 1a] Filtering relevant messages with detailed logging...")
        deduplicated_messages: Dict[str, Dict[str, Any]] = {}
        for i, msg in enumerate(messages):
            is_relevant = False
            log_details = []
            for name in self.model_names:
                pred = msg.get(f"{name}_prediction")
                conf = msg.get(f"{name}_confidence", 0)
                log_details.append(f"{name}: {pred} ({conf:.4f})")
                if pred == 'Relevant' and conf >= ANALYSIS_RELEVANCE_THRESHOLD:
                    is_relevant = True
            
            # --- Detailed Log for Each Message ---
            status = "KEPT" if is_relevant else "DISCARDED"
            print(f"     - Msg {i+1:03d} | {status} | Checks: [ {', '.join(log_details)} ] | Text: \"{msg.get('text', '')[:50]}...\"")

            if is_relevant:
                message_text = msg.get('text', '')
                if message_text and message_text not in deduplicated_messages:
                    deduplicated_messages[message_text] = msg
        
        return list(deduplicated_messages.values())

# Create a singleton instance for use across the application.
analysis_service = AnalysisService()
