from pathlib import Path

# --- General Settings ---
APP_NAME = "Forensic Chat Classification API"
VERSION = "1.0.0"

# --- Model Path ---
MODEL_DIR = Path("model")
MODEL_PATH = "model/gbert_binary_classifier/checkpoint-496"

# --- Classification Defaults ---
DEFAULT_SESSION_GAP = 10  # in minutes
DEFAULT_CONTEXT_WINDOW = 3  # messages before + current + next message
MAX_SEQUENCE_LENGTH = 512 # Bert Token limit

# --- Output Directories ---
OUTPUT_BASE = Path("data/outputs")
