# app/routes/folder_path.py

from fastapi import APIRouter, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pathlib import Path
import numpy as np
import pandas as pd

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.services.analysis_service import analysis_service  
from app.utils.logger import logger
from app.config import DEFAULT_SESSION_GAP, DEFAULT_CONTEXT_WINDOW
from app.utils.conversion import to_builtin_type

router = APIRouter()

@router.post("/classify-folder")
async def classify_folder_api(
    folder_path: str = Form(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
    explain: bool = Form(False),
    summarize: bool = Form(False),  
):
    """
    Handles the classification of all supported files within a given folder
    and all of its subfolders. Optionally runs RAG-based summary per file
    and at the global level if summarize=True.
    """
    folder = Path(folder_path)
    if not folder.is_dir():
        raise HTTPException(status_code=400, detail="Invalid folder path provided.")

    logger.info(f"Starting recursive classification for folder: {folder.resolve()}")

    file_outputs = {}
    errors = []

    all_messages_for_global_rag = []

    for file_path in folder.rglob("*"):
        if not file_path.is_file():
            continue

        relative_path = file_path.relative_to(folder)

        try:
            logger.info(f"Processing file: {relative_path}")
            file_entry = {}

            # --- Plain .txt files ---
            if file_path.suffix.lower() == ".txt":
                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                    content = f.read()

                results = classifier.classify_text(content, context_aware=context_aware)
                file_entry["results"] = results

                # Add all messages to global RAG
                all_messages_for_global_rag.extend(results)

                # Stats
                model_names = [k.replace("_prediction", "") for k in results[0].keys() if k.endswith("_prediction")] if results else []
                relevant_found = sum(
                    1 for r in results 
                    if any(r.get(f"{name}_prediction") == "Relevant" for name in model_names)
                )
                stats = {
                    "messages": len(results),
                    "relevant_messages": relevant_found,
                }
                file_entry["stats"] = stats
                file_entry["file_type"] = "txt"

                # RAG summary per file
                if summarize and results:
                    rag_report = analysis_service.cluster_and_summarize(results)
                    file_entry["rag_report"] = rag_report

                file_outputs[str(relative_path)] = file_entry

            # --- Structured files ---
            elif file_path.suffix.lower() in [".xlsx", ".csv", ".ods", ".db", ".sqlite"]:
                processor = BaseProcessor(
                    session_gap_minutes=session_gap_minutes,
                    context_window_size=context_window_size,
                )
                result = processor.detect_and_process(str(file_path), context_aware=context_aware)

                # Flatten all classified messages (from all sessions) for this file
                file_results = result.get("results", [])
                # Handle case where results may be sessionized (list of sessions with 'messages')
                flat_results = []
                if file_results and isinstance(file_results, list):
                    if isinstance(file_results[0], dict) and "messages" in file_results[0]:
                        for sess in file_results:
                            flat_results.extend(sess.get("messages", []))
                    else:
                        flat_results = file_results
                file_entry["results"] = file_results

                # Add to global messages
                all_messages_for_global_rag.extend(flat_results)

                file_entry["stats"] = result.get("stats", {})
                file_entry["platform"] = result.get("platform", "unknown")
                file_entry["file_type"] = "structured"

                # RAG summary per file
                if summarize and flat_results:
                    rag_report = analysis_service.cluster_and_summarize(flat_results)
                    file_entry["rag_report"] = rag_report

                file_outputs[str(relative_path)] = file_entry

        except Exception as e:
            logger.error(f"Failed to process file {relative_path}: {e}", exc_info=True)
            errors.append(f"{relative_path}: {str(e)}")

    # ---- Folder-wide global RAG summary ----
    global_rag_report = None
    if summarize and all_messages_for_global_rag:
        global_rag_report = analysis_service.cluster_and_summarize(all_messages_for_global_rag)

    logger.info(f"Folder classification complete for: {folder.resolve()}")
    return JSONResponse(jsonable_encoder(to_builtin_type({
        "status": "completed",
        "file_outputs": file_outputs,
        "global_rag_report": global_rag_report,
        "errors": errors,
    })))
