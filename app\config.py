from pathlib import Path
import os

#General Settings 
APP_NAME = "Forensic Chat Classification API"
VERSION = "3.0.0"

# Base Directory
BASE_DIR = Path(__file__).resolve().parent.parent

# Model Paths 
MODEL_DIR = BASE_DIR / "model"

# CLASSIFICATION MODELS
MODEL_PATHS = {
    "gbert-base": str(MODEL_DIR / "gbert_binary_classifier/checkpoint-496"),
    "gbert-large": str(MODEL_DIR / "gbert_large_balanced/checkpoint-580"),
}

# EMBEDDING MODEL
# Switched to a strong multilingual model for better German performance.
EMBEDDING_MODEL_PATH = "sentence-transformers/paraphrase-multilingual-mpnet-base-v2"

# LOCAL GERMAN LLM FOR ADVANCED SUMMARIZATION
LOCAL_LLM_PATH = str(MODEL_DIR / "em_german_leo_mistral-Q4_K_M.gguf")

# --- DEBUGGING: Print the path to verify it's correct ---
print("="*80)
print(f"DEBUG: Attempting to load local LLM from absolute path:")
print(f"'{LOCAL_LLM_PATH}'")
print(f"Does this file exist? {os.path.exists(LOCAL_LLM_PATH)}")
print("="*80)
# ---------------------------------------------------------


# Classification Defaults
DEFAULT_SESSION_GAP = 10  # in minutes
DEFAULT_CONTEXT_WINDOW = 3  # messages before + current + next message
MAX_SEQUENCE_LENGTH = 512 # Bert Token limit

# Threshold for a message to be considered "Relevant" by the initial classifiers.
ANALYSIS_RELEVANCE_THRESHOLD = 0.85

# Number of context messages (neighbors) to retrieve for each relevant message.
RAG_NEIGHBORS = 5

# This threshold is the maximum cosine distance for messages to be in the same cluster.
AGGLOMERATIVE_THRESHOLD = 0.23

# The minimum number of messages required to form a valid topic.
DBSCAN_MIN_SAMPLES = 2