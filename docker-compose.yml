services:
  backend:
    image: python:3.10-slim
    container_name: textclass_api
    working_dir: /app
    volumes:
      - .:/app
      - ${NAS_MOUNT}:/mnt/nas2
    ports:
      - "8500:8500"
    command: >
      /bin/sh -c "
        pip install --no-cache-dir -r requirements.txt &&
        uvicorn main:app --host 0.0.0.0 --port 8500
      "

  frontend:
    image: python:3.10-slim
    container_name: textclass_streamlit
    working_dir: /app
    volumes:
      - .:/app
      - ${NAS_MOUNT}:/mnt/nas2
    ports:
      - "8501:8501"
    command: >
      /bin/sh -c "
        pip install --no-cache-dir -r requirements.txt &&
        streamlit run gui/app.py --server.port 8501 --server.address 0.0.0.0
      "
