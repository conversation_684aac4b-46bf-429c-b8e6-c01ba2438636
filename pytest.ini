[tool:pytest]
# Pytest configuration for Forensic Text Classification API

# Test discovery
testpaths = tests/unit_tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    -ra

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    api: API endpoint tests
    parser: Parser tests
    classifier: Classifier tests
    service: Service tests
    utils: Utility function tests

# Minimum version requirements
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Ignore certain warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:transformers.*
    ignore::FutureWarning:transformers.*
