"""
Essential unit tests for utility functions
"""
import pytest
import json
from datetime import datetime, timezone

from app.utils.output_utils import (
    flatten_sessions,
    results_to_csv_string,
    results_to_jsonl_string,
    create_output_directory
)
from app.utils.logger import logger


class TestOutputUtils:
    """Essential tests for output utilities"""

    def test_flatten_sessions_empty(self):
        """Test empty sessions"""
        assert flatten_sessions([]) == []

    def test_flatten_sessions_basic(self):
        """Test basic session flattening"""
        sessions = [{
            "session_id": 1,
            "messages": [{"text": "msg1"}, {"text": "msg2"}]
        }]

        result = flatten_sessions(sessions)
        assert len(result) == 2
        assert result[0]["session_id"] == 1
        assert result[1]["session_id"] == 1

    def test_results_to_csv_string_empty(self):
        """Test empty CSV conversion"""
        assert results_to_csv_string([]).strip() == ""

    def test_results_to_csv_string_basic(self):
        """Test basic CSV conversion"""
        results = [{"text": "test", "prediction": "Relevant"}]
        csv_string = results_to_csv_string(results)

        assert "text,prediction" in csv_string
        assert "test,Relevant" in csv_string

    def test_results_to_jsonl_string_empty(self):
        """Test empty JSONL conversion"""
        assert results_to_jsonl_string([]).strip() == ""

    def test_results_to_jsonl_string_basic(self):
        """Test basic JSONL conversion"""
        results = [{"text": "msg1"}, {"text": "msg2"}]
        jsonl_string = results_to_jsonl_string(results)
        lines = jsonl_string.strip().split('\n')

        assert len(lines) == 2
        assert json.loads(lines[0])["text"] == "msg1"
        assert json.loads(lines[1])["text"] == "msg2"

    def test_create_output_directory(self, temp_dir):
        """Test output directory creation"""
        result_dir = create_output_directory(str(temp_dir / "test"))

        assert result_dir.exists()
        assert "results_" in result_dir.name


class TestLogger:
    """Essential tests for logger"""

    def test_logger_exists(self):
        """Test logger is available"""
        assert logger is not None
        assert hasattr(logger, 'info')
        assert hasattr(logger, 'error')
