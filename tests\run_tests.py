#!/usr/bin/env python3
"""
Test runner script for the Forensic Text Classification API

This script runs all unit tests and generates coverage reports.
"""
import sys
import subprocess
from pathlib import Path
import argparse

def run_tests(test_path=None, coverage=True, verbose=False, specific_test=None):
    """
    Run tests with optional coverage reporting
    
    Args:
        test_path: Specific test file or directory to run
        coverage: Whether to generate coverage report
        verbose: Whether to run in verbose mode
        specific_test: Specific test function to run
    """
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add coverage if requested
    if coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=html:tests/coverage_html",
            "--cov-report=term-missing",
            "--cov-fail-under=70"  # Require at least 70% coverage
        ])
    
    # Add verbosity
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Add specific test path or default to unit_tests
    if specific_test:
        cmd.append(f"tests/unit_tests/{specific_test}")
    elif test_path:
        cmd.append(test_path)
    else:
        cmd.append("tests/unit_tests/")
    
    # Add additional pytest options
    cmd.extend([
        "--tb=short",  # Shorter traceback format
        "--strict-markers",  # Strict marker checking
        "--disable-warnings"  # Disable warnings for cleaner output
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1

def main():
    """Main function to parse arguments and run tests"""
    parser = argparse.ArgumentParser(description="Run unit tests for the Forensic Text Classification API")
    
    parser.add_argument(
        "--path", "-p",
        help="Specific test file or directory to run",
        default=None
    )
    
    parser.add_argument(
        "--no-coverage", "-nc",
        action="store_true",
        help="Skip coverage reporting"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    
    parser.add_argument(
        "--test", "-t",
        help="Run a specific test file (e.g., test_text_classifier.py)",
        default=None
    )
    
    parser.add_argument(
        "--list-tests", "-l",
        action="store_true",
        help="List all available test files"
    )
    
    args = parser.parse_args()
    
    # List available tests
    if args.list_tests:
        test_dir = Path("tests/unit_tests")
        if test_dir.exists():
            print("Available test files:")
            for test_file in sorted(test_dir.glob("test_*.py")):
                print(f"  - {test_file.name}")
        else:
            print("No test directory found at tests/unit_tests/")
        return 0
    
    # Run tests
    coverage = not args.no_coverage
    return_code = run_tests(
        test_path=args.path,
        coverage=coverage,
        verbose=args.verbose,
        specific_test=args.test
    )
    
    if coverage and return_code == 0:
        print("\n" + "=" * 60)
        print("Coverage report generated in: tests/coverage_html/index.html")
        print("=" * 60)
    
    return return_code

if __name__ == "__main__":
    sys.exit(main())
