"""
Unit tests for service classes
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone, timedelta
from pathlib import Path

from app.services.sessionizer import Sessionizer
from app.services.base_processor import BaseProcessor
from app.services.whatsapp_processor import WhatsAppProcessor
from app.services.analysis_service import AnalysisService


class TestSessionizer:
    """Test cases for Sessionizer"""
    
    def test_init(self):
        """Test Sessionizer initialization"""
        sessionizer = Sessionizer(session_gap=timedelta(minutes=30))
        assert sessionizer.session_gap == timedelta(minutes=30)
    
    def test_sessionize_empty_messages(self):
        """Test sessionizing empty message list"""
        sessionizer = Sessionizer()
        result = sessionizer.sessionize([])
        assert result == []
    
    def test_sessionize_single_message(self, sample_messages):
        """Test sessionizing single message"""
        sessionizer = Sessionizer()
        single_message = [sample_messages[0]]
        
        result = sessionizer.sessionize(single_message)
        
        assert len(result) == 1
        assert result[0]["session_id"] == 1
        assert len(result[0]["messages"]) == 1
    
    def test_sessionize_multiple_sessions(self, sample_messages):
        """Test sessionizing messages into multiple sessions"""
        sessionizer = Sessionizer(session_gap=timedelta(minutes=3))
        
        # Modify timestamps to create clear session breaks
        messages = sample_messages.copy()
        messages[0]["timestamp"] = datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        messages[1]["timestamp"] = datetime(2024, 1, 1, 10, 1, 0, tzinfo=timezone.utc)  # Same session
        messages[2]["timestamp"] = datetime(2024, 1, 1, 10, 10, 0, tzinfo=timezone.utc)  # New session
        
        result = sessionizer.sessionize(messages)
        
        assert len(result) == 2  # Should create 2 sessions
        assert result[0]["session_id"] == 1
        assert result[1]["session_id"] == 2
        assert len(result[0]["messages"]) == 2
        assert len(result[1]["messages"]) == 1
    
    def test_sessionize_preserves_message_data(self, sample_messages):
        """Test that sessionizing preserves original message data"""
        sessionizer = Sessionizer()
        result = sessionizer.sessionize(sample_messages)
        
        original_message = sample_messages[0]
        sessionized_message = result[0]["messages"][0]
        
        assert sessionized_message["text"] == original_message["text"]
        assert sessionized_message["sender"] == original_message["sender"]
        assert sessionized_message["timestamp"] == original_message["timestamp"]


class TestBaseProcessor:
    """Test cases for BaseProcessor"""
    
    def test_init(self):
        """Test BaseProcessor initialization"""
        processor = BaseProcessor(session_gap_minutes=20, context_window_size=5)
        assert processor.session_gap_minutes == 20
        assert processor.context_window_size == 5
    
    def test_detect_and_process_nonexistent_path(self):
        """Test processing non-existent path"""
        processor = BaseProcessor()
        
        with pytest.raises(FileNotFoundError):
            processor.detect_and_process("nonexistent/path")
    
    def test_detect_and_process_txt_file(self, sample_text_file):
        """Test processing .txt file raises error"""
        processor = BaseProcessor()
        
        with pytest.raises(ValueError, match=".txt \\(raw text\\) files should not be passed"):
            processor.detect_and_process(str(sample_text_file))
    
    @patch('app.services.base_processor.WhatsAppProcessor')
    def test_detect_and_process_whatsapp_success(self, mock_whatsapp_processor, sample_whatsapp_xlsx):
        """Test successful WhatsApp processing"""
        # Setup mock
        mock_processor_instance = Mock()
        mock_processor_instance.process_whatsapp_data.return_value = {
            "results": [],
            "stats": {"messages": 0}
        }
        mock_whatsapp_processor.return_value = mock_processor_instance
        
        processor = BaseProcessor()
        result = processor.detect_and_process(str(sample_whatsapp_xlsx))
        
        assert result["platform"] == "whatsapp"
        mock_whatsapp_processor.assert_called_once()
        mock_processor_instance.process_whatsapp_data.assert_called_once()
    
    @patch('app.services.base_processor.WhatsAppProcessor')
    def test_detect_and_process_whatsapp_failure(self, mock_whatsapp_processor, sample_whatsapp_xlsx):
        """Test WhatsApp processing failure"""
        # Setup mock to raise exception
        mock_processor_instance = Mock()
        mock_processor_instance.process_whatsapp_data.side_effect = Exception("Processing failed")
        mock_whatsapp_processor.return_value = mock_processor_instance
        
        processor = BaseProcessor()
        
        with pytest.raises(ValueError, match="Unsupported or unrecognized platform"):
            processor.detect_and_process(str(sample_whatsapp_xlsx))


class TestWhatsAppProcessor:
    """Test cases for WhatsAppProcessor"""
    
    def test_init(self):
        """Test WhatsAppProcessor initialization"""
        processor = WhatsAppProcessor(session_gap_minutes=15, context_window_size=4)
        assert processor.session_gap == timedelta(minutes=15)
        assert processor.context_window_size == 4
        assert hasattr(processor, 'parser')
        assert hasattr(processor, 'model_names')
    
    @patch('app.services.whatsapp_processor.WhatsAppParser')
    @patch('app.services.whatsapp_processor.classifier')
    def test_process_whatsapp_data_success(self, mock_classifier, mock_parser_class, temp_dir):
        """Test successful WhatsApp data processing"""
        # Setup mocks
        mock_parser = Mock()
        mock_parser.detect_platform_files.return_value = {
            "whatsapp_exports": [temp_dir / "test.xlsx"],
            "whatsapp_databases": []
        }
        mock_parser.parse_file.return_value = [
            {
                "timestamp": datetime.now(timezone.utc),
                "text": "Test message",
                "sender": "user1",
                "chat_id": "test_chat",
                "platform": "whatsapp"
            }
        ]
        mock_parser_class.return_value = mock_parser
        
        mock_classifier.classify_standard_inputs.return_value = [
            {
                "timestamp": datetime.now(timezone.utc),
                "text": "Test message",
                "sender": "user1",
                "gbert-base_prediction": "Relevant",
                "gbert-base_confidence": 0.85
            }
        ]
        
        # Create test file
        test_file = temp_dir / "test.xlsx"
        test_file.touch()
        
        processor = WhatsAppProcessor(session_gap_minutes=10, context_window_size=3)
        result = processor.process_whatsapp_data(str(temp_dir))
        
        assert "results" in result
        assert "stats" in result
        assert isinstance(result["results"], list)
    
    def test_process_whatsapp_data_no_files(self, temp_dir):
        """Test processing with no WhatsApp files found"""
        processor = WhatsAppProcessor(session_gap_minutes=10, context_window_size=3)
        
        with pytest.raises(ValueError, match="No WhatsApp files found"):
            processor.process_whatsapp_data(str(temp_dir))


class TestAnalysisService:
    """Test cases for AnalysisService"""
    
    def test_init(self):
        """Test AnalysisService initialization"""
        service = AnalysisService()
        assert service.embedding_model is None
        assert service.summarizer is None
        assert service._initialized is False
        assert hasattr(service, 'model_names')
    
    @patch('app.services.analysis_service.Path')
    @patch('app.services.analysis_service.SentenceTransformer')
    @patch('app.services.analysis_service.Llama')
    def test_ensure_models_loaded_success(self, mock_llama, mock_sentence_transformer, mock_path):
        """Test successful model loading"""
        # Setup mocks
        mock_path.return_value.exists.return_value = True
        mock_sentence_transformer.return_value = Mock()
        mock_llama.return_value = Mock()
        
        service = AnalysisService()
        service._ensure_models_loaded()
        
        assert service._initialized is True
        assert service.embedding_model is not None
        assert service.summarizer is not None
    
    @patch('app.services.analysis_service.Path')
    def test_ensure_models_loaded_missing_file(self, mock_path):
        """Test model loading with missing LLM file"""
        mock_path.return_value.exists.return_value = False
        
        service = AnalysisService()
        
        with pytest.raises(FileNotFoundError):
            service._ensure_models_loaded()
    
    def test_health_check_uninitialized(self):
        """Test health check when models are not initialized"""
        service = AnalysisService()
        
        with patch('app.services.analysis_service.Path') as mock_path:
            mock_path.return_value.exists.return_value = True
            
            # Mock _ensure_models_loaded to avoid actual model loading
            with patch.object(service, '_ensure_models_loaded'):
                result = service.health_check()
                
                assert "status" in result
                assert "model_file_exists" in result
                assert "models_loaded" in result
                assert "model_path" in result
    
    def test_health_check_error(self):
        """Test health check when an error occurs"""
        service = AnalysisService()
        
        with patch('app.services.analysis_service.Path') as mock_path:
            mock_path.return_value.exists.side_effect = Exception("Test error")
            
            result = service.health_check()
            
            assert result["status"] == "unhealthy"
            assert "error" in result
    
    @patch.object(AnalysisService, '_ensure_models_loaded')
    @patch.object(AnalysisService, '_filter_and_deduplicate')
    def test_cluster_and_summarize_insufficient_messages(self, mock_filter, mock_ensure_loaded):
        """Test cluster_and_summarize with insufficient relevant messages"""
        mock_filter.return_value = [{"text": "single message"}]  # Only 1 message
        
        service = AnalysisService()
        result = service.cluster_and_summarize([])
        
        assert result["status"] == "completed"
        assert "Not enough relevant messages" in result["summary"]
        assert result["clusters"] == []
    
    def test_filter_and_deduplicate_empty(self):
        """Test filtering with empty message list"""
        service = AnalysisService()
        service.model_names = ["gbert-base", "gbert-large"]
        
        result = service._filter_and_deduplicate([])
        assert result == []
    
    def test_filter_and_deduplicate_no_relevant(self):
        """Test filtering with no relevant messages"""
        service = AnalysisService()
        service.model_names = ["gbert-base"]
        
        messages = [
            {
                "text": "irrelevant message",
                "gbert-base_prediction": "Non-Relevant",
                "gbert-base_confidence": 0.9
            }
        ]
        
        result = service._filter_and_deduplicate(messages)
        assert result == []
    
    def test_filter_and_deduplicate_with_relevant(self):
        """Test filtering with relevant messages"""
        service = AnalysisService()
        service.model_names = ["gbert-base"]
        
        messages = [
            {
                "text": "relevant message",
                "gbert-base_prediction": "Relevant",
                "gbert-base_confidence": 0.9
            },
            {
                "text": "relevant message",  # Duplicate
                "gbert-base_prediction": "Relevant", 
                "gbert-base_confidence": 0.85
            },
            {
                "text": "another relevant message",
                "gbert-base_prediction": "Relevant",
                "gbert-base_confidence": 0.88
            }
        ]
        
        result = service._filter_and_deduplicate(messages)
        
        # Should deduplicate and return 2 unique messages
        assert len(result) == 2
        texts = [msg["text"] for msg in result]
        assert "relevant message" in texts
        assert "another relevant message" in texts
