{"_name_or_path": "deepset/gbert-large", "architectures": ["BertForSequenceClassification"], "attention_probs_dropout_prob": 0.2, "classifier_dropout": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.3, "hidden_size": 1024, "initializer_range": 0.02, "intermediate_size": 4096, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "model_type": "bert", "num_attention_heads": 16, "num_hidden_layers": 24, "pad_token_id": 0, "position_embedding_type": "absolute", "torch_dtype": "float32", "transformers_version": "4.40.2", "type_vocab_size": 2, "use_cache": true, "vocab_size": 31102}