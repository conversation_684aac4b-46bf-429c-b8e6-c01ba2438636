from abc import ABC, abstractmethod
from typing import List, Dict, Any
from pathlib import Path

class BaseSocialMediaParser(ABC):
    """Abstract base class for all social media parsers"""

    @classmethod
    @abstractmethod
    def detect_platform_files(cls, input_path: Path) -> Dict[str, List[Path]]:
        """Detect platform-specific files in the input directory"""
        pass

    @abstractmethod
    def parse_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """Parse a single file into normalized messages"""
        pass

    @abstractmethod
    def normalize_message(self, message: Dict) -> Dict:
        """Normalize message fields to common format"""
        pass

    @staticmethod
    def clean_message_text(text: str) -> str:
        """Common text cleaning for all platforms"""
        if not isinstance(text, str):
            return ""
        return text.strip()
