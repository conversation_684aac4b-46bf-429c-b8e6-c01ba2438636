from pathlib import Path
import os

# --- General Settings ---
APP_NAME = "Forensic Chat Classification API"
VERSION = "4.3.0" # RAG with absolute path debugging

# --- Base Directory ---
# This creates a reliable, absolute path to the project's root directory.
# It finds the location of this config file and goes up one level.
# __file__ is the path to the current file (config.py)
# .resolve() makes it an absolute path
# .parent gives the directory containing it (app/)
# .parent again gives the project root (TextClassification_API/)
BASE_DIR = Path(__file__).resolve().parent.parent

# --- Model Paths ---
MODEL_DIR = BASE_DIR / "model"

# A. CLASSIFICATION MODELS
MODEL_PATHS = {
    "gbert-base": str(MODEL_DIR / "gbert_binary_classifier/checkpoint-496"),
    "gbert-large": str(MODEL_DIR / "gbert_large_balanced/checkpoint-580"),
}

# B. EMBEDDING MODEL
EMBEDDING_MODEL_PATH = "all-MiniLM-L6-v2"

# C. LOCAL GERMAN LLM FOR ADVANCED SUMMARIZATION
# We build the absolute path to ensure the model is always found.
LOCAL_LLM_PATH = str(MODEL_DIR / "em_german_leo_mistral-Q4_K_M.gguf")

# --- DEBUGGING: Print the path to verify it's correct ---
print("="*80)
print(f"DEBUG: Attempting to load local LLM from absolute path:")
print(f"'{LOCAL_LLM_PATH}'")
print(f"Does this file exist? {os.path.exists(LOCAL_LLM_PATH)}")
print("="*80)
# ---------------------------------------------------------


# --- Analysis & Classification Defaults ---
DEFAULT_SESSION_GAP = 10
DEFAULT_CONTEXT_WINDOW = 3
MAX_SEQUENCE_LENGTH = 512

# D. RAG THRESHOLD
ANALYSIS_RELEVANCE_THRESHOLD = 0.85
