# Forensic Text Classification API

A powerful FastAPI-based system for classifying German-language forensic messages as **Relevant** or **Non-Relevant**, with robust support for WhatsApp and Facebook Messenger data in various formats.

## Core Capabilities

- German BERT-based binary classification model
- Context-aware message analysis with configurable window sizes
- Multi-format input support:
  - Raw text input
  - WhatsApp exports (.xlsx, .csv, .ods)
  - Decrypted WhatsApp databases (.db, .sqlite)
  - Batch processing of entire folder trees
- Automatic chat session detection and chronological chunking
- Token-level explanation of classification decisions
- Structured output in multiple formats (CSV, JSONL)

## Architecture Overview

```
textclassification_api/
├── app/
│   ├── main.py                   # FastAPI entry point
│   ├── config.py                 # Global settings
│   ├── routes/                   # API endpoints
│   ├── services/                 # Core processing logic
│   ├── platform/                 # social media parsers
│   ├── core/                     # Model and classifier
│   ├── utils/                    # Helper functions
├── model/                        # Trained model files
├── gui/                          # Streamlit interface
│   ├── app.py                    # Main GUI entry point
│   ├── pages/                    # Additional GUI pages
│   │   ├── 00_Help.py            # Help & supported formats
├── tests/                        # Test cases
│   ├── whatsapp_exports/         # XLSX, CSV, ODS samples
│   ├── whatsapp_databases/       # msgstore.db, ChatStorage.sqlite
│   ├── txt_samples/              # Cleaned + edge case texts
│   └── output_samples/           # To be generated during testing
├── requirements.txt
├── docker-compose.yml
```

## Quick Start

### Local Development

```bash
# Set up environment
python -m venv venv && source venv/bin/activate
pip install -r requirements.txt

# Run the FastAPI server
uvicorn app.main:app --host 0.0.0.0 --port 8500

# Run the Streamlit interface (optional)
streamlit run gui/app.py
```

### Docker Deployment

```bash
# Start both backend and frontend
docker-compose up -d

# Access the interfaces
API: http://localhost:8500/docs
GUI: http://localhost:8501
```

## Input Processing

| Input Type | Processing Pipeline |
|------------|---------------------|
| Raw text | Line-by-line or context-window classification |
| WhatsApp exports (.xlsx, .csv) | Parse → Sessionize → Classify with context |
| WhatsApp databases (.db) | Extract messages → Normalize → Classify |
| Folder path | Recursively process all supported files |

## Test Data Coverage (under `tests/`)

- WhatsApp exports (group/single) in `.xlsx`, `.csv`, `.ods`
- Realistic decrypted databases: `msgstore.db`, `ChatStorage.sqlite`
- Edge cases: mixed language, emojis, media omitted, duplicates
- Session variety: 1–2 messages to 100+ messages per session
- Text-only inputs: plain `.txt`, empty lines, malformed

## Classification Modes

| Input Type   | Parser Used              | Classification Logic                  |
|--------------|--------------------------|----------------------------------------|
| `.txt`       | `TextProcessor`          | Line-by-line classification            |
| `.xlsx/.csv` | `WhatsAppParser`/        | Sessionize + context-window prediction |
| `.db`        | `WhatsAppParser`         | Extract messages from DB and classify  |

## GUI Features

The Streamlit GUI provides an intuitive interface with:

- Multiple input methods (text, file upload, folder path)
- Context-aware scanning toggle
- Explanation of predictions using transformers-interpret
- Overview dashboard for folder analysis with:
  - Summary statistics
  - Table view of all processed files
  - Pie chart visualization of relevant messages
  - Detailed file inspection
- Help page with supported file formats and usage instructions

## Configuration

Key settings in `app/config.py`:

```python
MODEL_PATH = "model/gbert_binary_classifier/checkpoint-496"
DEFAULT_SESSION_GAP = 10        # minutes between sessions
DEFAULT_CONTEXT_WINDOW = 3      # messages for context
MAX_SEQUENCE_LENGTH = 512       # BERT token limit
```

## API Endpoints

- `POST /api/classify-text` - Process raw text input
- `POST /api/upload-file` - Process single file upload
- `POST /api/classify-folder` - Process entire folder tree

## Future Development

- Support for additional platforms (Signal, Telegram, Skype)
- Feedback API for analyst corrections
- Production deployment optimizations
- Enhanced visualization of classification results

## Wiki Documentation

For detailed documentation please refer to the [wiki page](https://wiki.digifors.de/books/forensic-text-classification-model-training-to-api-deployment)

## Contact

- Venati Himanth - <EMAIL>


Todo's:
# Fix Context View for Text Files
Ensure the original raw lines (not [SEP] merged chunks) are shown correctly in the GUI when context-aware mode is enabled for .txt files.

# Support Subfolder Scanning in Folder Mode
Extend /api/classify-folder to recursively scan subdirectories, allowing deeper forensic analysis of nested folder structures.

# Improve Overview Pie Chart (Frontend)
Enhance the pie chart visualization with clearer labels, interactive filters, and a more user-friendly layout for large-scale folder scans.

# Add Facebook Messenger Support
Implement a parser and processor for Facebook Messenger chat exports, ensuring compatibility with .json and .html formats.

