# Forensic Text Classification API

A powerful FastAPI-based system for classifying German-language forensic messages as **Relevant** or **Non-Relevant**, with robust support for WhatsApp data processing and advanced RAG-based analysis capabilities.

## Core Capabilities

- **Multi-Model Classification**: Dual German BERT models (gbert-base, gbert-large) for enhanced accuracy
- **Context-Aware Analysis**: Configurable message window sizes for contextual understanding
- **Advanced RAG Pipeline**: German LeoLM-powered clustering and summarization of relevant content
- **Multi-Format Input Support**:
  - Raw text input with line-by-line processing
  - WhatsApp exports (.xlsx, .csv, .ods) with XAMN compatibility
  - Decrypted WhatsApp databases (.db, .sqlite) with schema auto-detection
  - Batch processing of entire folder trees with recursive scanning
- **Intelligent Session Detection**: Automatic chat session chunking based on time gaps
- **Explainable AI**: Token-level explanation of classification decisions
- **Flexible Output**: Structured results in CSV, JSONL, and session-based formats

## Architecture Overview

```
textclassification_api/
├── app/
│   ├── main.py                   # FastAPI entry point with startup/shutdown hooks
│   ├── config.py                 # Global settings and model paths
│   ├── routes/                   # API endpoints
│   │   ├── raw_text.py           # Text classification and health checks
│   │   ├── file_upload.py        # Single file processing
│   │   └── folder_path.py        # Batch folder processing
│   ├── services/                 # Core processing logic
│   │   ├── analysis_service.py   # RAG pipeline with German LeoLM
│   │   ├── whatsapp_processor.py # WhatsApp-specific processing
│   │   ├── sessionizer.py        # Message grouping by time gaps
│   │   └── base_processor.py     # Platform detection and routing
│   ├── platform/                 # Social media parsers
│   │   ├── whatsapp_parser.py    # Export file parsing (.xlsx, .csv, .ods)
│   │   ├── whatsapp_db_parser.py # Database parsing (.db, .sqlite)
│   │   └── base_parser.py        # Abstract parser interface
│   ├── core/                     # Model and classifier
│   │   ├── text_classifier.py    # Multi-model ensemble classifier
│   │   └── ensemble_classifier.py # Model combination logic
│   ├── utils/                    # Helper functions
│   │   ├── logger.py             # Centralized logging
│   │   └── output_utils.py       # CSV/JSONL export utilities
│   └── data/outputs/             # Generated classification results
├── model/                        # Trained model files
│   ├── gbert_binary_classifier/  # Base BERT model
│   ├── gbert_large_balanced/     # Large BERT model
│   └── em_german_leo_mistral-Q4_K_M.gguf # German LLM for analysis
├── gui/                          # Streamlit interface
│   ├── app.py                    # Main GUI entry point
│   └── pages/                    # Additional GUI pages
├── logs/                         # Application logs
├── tests/                        # Test cases and sample data
└── requirements.txt              # Python dependencies
```

## Quick Start

### Local Development

```bash
# Set up environment
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run the FastAPI server (from project root)
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Alternative: Run from app directory
cd app
python run_server.py  # Uses wrapper script to avoid circular imports

# Run the Streamlit interface (optional)
streamlit run gui/app.py
```

### Docker Deployment

```bash
# Start both backend and frontend
docker-compose up -d

# Access the interfaces
API: http://localhost:8500/docs
GUI: http://localhost:8501
Health Check: http://localhost:8500/analysis-service-health
```

## Processing Pipeline

| Input Type | Processing Steps | Output Format |
|------------|------------------|---------------|
| **Raw text** | Line-by-line → Context windows → Classification | Individual results per line |
| **WhatsApp exports** | Parse (.xlsx/.csv/.ods) → Validate → Sessionize → Classify → RAG Analysis | Session-based results with summaries |
| **WhatsApp databases** | Extract (.db/.sqlite) → Schema detection → Normalize → Classify | Chronological message results |
| **Folder processing** | Recursive scan → Auto-detect formats → Batch process → Aggregate | Combined results across all files |

### Advanced Analysis Features

- **RAG Pipeline**: Relevant messages are clustered using FAISS vector similarity and summarized with German LeoLM
- **Multi-Model Ensemble**: Combines predictions from multiple BERT variants for higher accuracy
- **Flexible Validation**: Adaptive validation for different WhatsApp export formats (XAMN, standard exports)
- **Session Intelligence**: Automatic detection of conversation boundaries based on time gaps

## Test Data Coverage (under `tests/`)

- WhatsApp exports (group/single) in `.xlsx`, `.csv`, `.ods`
- Realistic decrypted databases: `msgstore.db`, `ChatStorage.sqlite`
- Edge cases: mixed language, emojis, media omitted, duplicates
- Session variety: 1–2 messages to 100+ messages per session
- Text-only inputs: plain `.txt`, empty lines, malformed

## Model Architecture

### Classification Models
- **gbert-base**: Primary German BERT model (checkpoint-496)
- **gbert-large**: Enhanced German BERT model (checkpoint-580)
- **Ensemble Logic**: Combines predictions for improved accuracy

### Analysis Models
- **Embedding Model**: all-MiniLM-L6-v2 for semantic similarity
- **German LLM**: em_german_leo_mistral-Q4_K_M.gguf for summarization
- **Vector Database**: FAISS for efficient similarity search
- **Clustering**: DBSCAN for topic grouping

### Processing Modes

| Input Type | Parser | Classification | Analysis |
|------------|--------|----------------|----------|
| `.txt` | Direct processing | Line-by-line with context windows | Individual message analysis |
| `.xlsx/.csv/.ods` | WhatsAppParser | Session-based with temporal context | RAG clustering and summarization |
| `.db/.sqlite` | WhatsAppDBParser | Database extraction with normalization | Full conversation analysis |

## GUI Features

The Streamlit GUI provides an intuitive interface with:

- Multiple input methods (text, file upload, folder path)
- Context-aware scanning toggle
- Explanation of predictions using transformers-interpret
- Overview dashboard for folder analysis with:
  - Summary statistics
  - Table view of all processed files
  - Pie chart visualization of relevant messages
  - Detailed file inspection
- Help page with supported file formats and usage instructions

## Configuration

Key settings in `app/config.py`:

```python
# Model Paths 
MODEL_PATHS = {
    "gbert-base": "model/gbert_binary_classifier/checkpoint-496",
    "gbert-large": "model/gbert_large_balanced/checkpoint-580",
}
LOCAL_LLM_PATH = "model/em_german_leo_mistral-Q4_K_M.gguf"
EMBEDDING_MODEL_PATH = "all-MiniLM-L6-v2"

# Processing Settings
DEFAULT_SESSION_GAP = 10        # minutes between sessions
DEFAULT_CONTEXT_WINDOW = 3      # messages for context
MAX_SEQUENCE_LENGTH = 512       # BERT token limit
ANALYSIS_RELEVANCE_THRESHOLD = 0.85  # RAG analysis threshold
```

## API Endpoints

### Core Classification
- `POST /classify-text` - Process raw text input with optional context awareness
- `POST /upload-file` - Process single file upload (supports .txt, .xlsx, .csv, .ods, .db)
- `POST /classify-folder` - Process entire folder tree recursively

### Health & Monitoring
- `GET /analysis-service-health` - Check RAG analysis service status
- `GET /docs` - Interactive API documentation
- `GET /` - API status and version information

## Future Development

- Support for additional platforms (Signal, Telegram, Skype)
- Feedback API for analyst corrections
- Production deployment optimizations
- Enhanced visualization of classification results

## Wiki Documentation

For detailed documentation please refer to the [wiki page](https://wiki.digifors.de/books/forensic-text-classification-model-training-to-api-deployment)

## Contact

- Venati Himanth - <EMAIL>


Todo's:
# Fix Context View for Text Files
Ensure the original raw lines (not [SEP] merged chunks) are shown correctly in the GUI when context-aware mode is enabled for .txt files.

# Support Subfolder Scanning in Folder Mode
Extend /api/classify-folder to recursively scan subdirectories, allowing deeper forensic analysis of nested folder structures.

# Improve Overview Pie Chart (Frontend)
Enhance the pie chart visualization with clearer labels, interactive filters, and a more user-friendly layout for large-scale folder scans.

# Add Facebook Messenger Support
Implement a parser and processor for Facebook Messenger chat exports, ensuring compatibility with .json and .html formats.

