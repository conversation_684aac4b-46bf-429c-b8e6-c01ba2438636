from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any
from pathlib import Path
import io, csv, json

from app.platform.whatsapp_parser import WhatsAppParser
from app.utils.logger import logger
from app.services.sessionizer import Sessionizer
from app.core.text_classifier import classifier

def to_csv_string(sessions):
    rows = []
    for sess in sessions:
        sid = sess.get("session_id", "")
        for msg in sess.get("messages", []):
            row = msg.copy()
            row["session_id"] = sid
            rows.append(row)
    if not rows:
        return ""
    keys = sorted({k for row in rows for k in row})
    out = io.StringIO()
    writer = csv.DictWriter(out, fieldnames=keys)
    writer.writeheader()
    for row in rows:
        for k, v in row.items():
            if hasattr(v, "isoformat"):
                row[k] = v.isoformat()
        writer.writerow(row)
    return out.getvalue()


def to_jsonl_string(sessions):
    lines = []
    for sess in sessions:
        sid = sess.get("session_id", "")
        for msg in sess.get("messages", []):
            row = msg.copy()
            row["session_id"] = sid
            for k, v in row.items():
                if hasattr(v, "isoformat"):
                    row[k] = v.isoformat()
            lines.append(json.dumps(row, ensure_ascii=False))
    return "\n".join(lines)


# ────────────────── main processor class ─────────────────────────────────
class WhatsAppProcessor:
    def __init__(self, session_gap_minutes: int = 30, context_window_size: int = 3):
        self.parser = WhatsAppParser()
        self.session_gap = timedelta(minutes=session_gap_minutes)
        self.context_window_size = context_window_size

    # ------------------------------------------------------------------ #
    def process_whatsapp_data(
        self, input_path: str, *, context_aware: bool = False
    ) -> Dict[str, Any]:
        """
        Parse WhatsApp exports / DBs, classify, sessionise and export.
        Returns dict with keys: results, stats, csv_string, jsonl_string
        """
        files = self.parser.detect_platform_files(Path(input_path))
        if not files["exports"] and not files["databases"]:
            raise ValueError("No WhatsApp files found in the input path")

        # ---------- 1. parse all files ------------------------------------
        all_messages = []
        for f in files["exports"] + files["databases"]:
            try:
                logger.info(f"📄 Parsing WhatsApp file: {f.name}")
                all_messages.extend(self.parser.parse_file(f))
            except Exception as e:
                logger.warning(f"Failed to process {f.name}: {e}")

        if not all_messages:
            raise ValueError("No valid messages found in the input files")

        # ---------- 2. sessionise ----------------------------------------
        sessionizer = Sessionizer(self.session_gap, self.context_window_size)
        sessions = sessionizer.create_sessions(all_messages)

        # ---------- 3. classify messages ---------------------------------
        all_classified = []
        for sess in sessions:
            msgs = sess.get("messages", [])
            if not msgs:
                continue

            preds = classifier.classify_standard_inputs(
                msgs, context_aware=context_aware
            )

            if not context_aware:
                for m in msgs:
                    m.pop("context", None)

            for i, m in enumerate(msgs):
                # patched ↓ (safe .get)
                m["label"]      = preds[i].get("label", preds[i].get("prediction"))
                m["prediction"] = preds[i].get("prediction")
                m["confidence"] = preds[i].get("confidence")
                m["session_id"] = sess.get("session_id")

            sess["messages"] = msgs
            all_classified.extend(msgs)

        # ---------- 4. tidy structure ------------------------------------
        structured_sessions = []
        for idx, sess in enumerate(sessions):
            msgs = sess.get("messages", [])
            if not msgs:
                continue

            structured = {
                "session_id": sess.get("session_id", f"session_{idx+1:03}"),
                "start_time": msgs[0]["timestamp"],
                "end_time": msgs[-1]["timestamp"],
                "num_messages": len(msgs),
                "num_relevant": 0,
                "messages": [],
            }

            for m in msgs:
                label = m.get("label", "unknown")
                if label.lower() in {"relevant", "1"}:
                    structured["num_relevant"] += 1
                structured["messages"].append(
                    {
                        "timestamp": m.get("timestamp"),
                        "sender": m.get("sender"),
                        "text": m.get("text"),
                        "label": label,
                        "prediction": label,
                        "confidence": m.get("confidence"),
                        "chat_id": m.get("chat_id"),
                        "context": m.get("context"),
                    }
                )

            structured_sessions.append(structured)

        # ---------- 5. exports & stats -----------------------------------
        csv_string = to_csv_string(structured_sessions)
        jsonl_string = to_jsonl_string(structured_sessions)

        return {
            "results": structured_sessions,
            "stats": {
                "chats": len({m["chat_id"] for m in all_classified}),
                "sessions": len(structured_sessions),
                "messages": len(all_classified),
                "relevant_messages": sum(s["num_relevant"] for s in structured_sessions),
            },
            "csv_string": csv_string,
            "jsonl_string": jsonl_string,
        }
