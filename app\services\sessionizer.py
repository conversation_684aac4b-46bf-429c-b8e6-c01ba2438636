from datetime import timedelta
from typing import List, Dict, Any, Union
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

class Sessionizer:
    def __init__(self, session_gap: Union[int, timedelta], context_window_size: int = 3):
        self.session_gap = timedelta(minutes=session_gap) if isinstance(session_gap, int) else session_gap
        self.context_window_size = context_window_size

    def create_sessions(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        chats = defaultdict(list)
        for msg in messages:
            chat_id = msg.get('chat_id')
            timestamp = msg.get('timestamp')
            if chat_id and timestamp:
                chats[chat_id].append(msg)

        all_sessions = []

        for chat_id, chat_messages in chats.items():
            sorted_messages = sorted(chat_messages, key=lambda x: x['timestamp'])
            if not sorted_messages:
                continue

            sessions = []
            current = {
                'chat_id': chat_id,
                'start_time': sorted_messages[0]['timestamp'],
                'end_time': sorted_messages[0]['timestamp'],
                'messages': [sorted_messages[0]]
            }

            for msg in sorted_messages[1:]:
                ts = msg['timestamp']
                if ts - current['end_time'] <= self.session_gap:
                    current['messages'].append(msg)
                    current['end_time'] = ts
                else:
                    sessions.append(current)
                    current = {
                        'chat_id': chat_id,
                        'start_time': ts,
                        'end_time': ts,
                        'messages': [msg]
                    }

            sessions.append(current)

            for i, session in enumerate(sessions, 1):
                safe_id = chat_id.replace("@", "_").replace(":", "_").replace("/", "_")
                session['session_id'] = f"{safe_id}_session_{i}"
                session['message_count'] = len(session['messages'])
                self._add_context_windows(session["messages"])

            all_sessions.extend(sessions)

        return all_sessions

    def _add_context_windows(self, messages: List[Dict[str, Any]]):
        for i, msg in enumerate(messages):
            left = max(0, i - self.context_window_size)
            right = min(len(messages), i + self.context_window_size + 1)
            context_messages = messages[left:right]
            context_text = [m.get("text", "") for m in context_messages if m.get("text")]
            msg["context"] = " [SEP] ".join(context_text)
            logger.debug(f"[Sessionizer] Msg #{i+1} context: {msg['context']}")

