from pathlib import Path

# --- General Settings ---
APP_NAME = "Forensic Chat Classification API"
VERSION = "4.2.0" # Advanced RAG with German LeoLM Model (Path Fix)

# --- Base Directory ---
# This creates a reliable, absolute path to the project's root directory.
BASE_DIR = Path(__file__).resolve().parent.parent

# --- Model Paths ---
MODEL_DIR = BASE_DIR / "model"

# A. CLASSIFICATION MODELS (No change here)
MODEL_PATHS = {
    "gbert-base": str(MODEL_DIR / "gbert_binary_classifier/checkpoint-496"),
    "gbert-large": str(MODEL_DIR / "gbert_large_balanced/checkpoint-580"),
}

# B. EMBEDDING MODEL (No change here)
EMBEDDING_MODEL_PATH = "all-MiniLM-L6-v2"

# C. LOCAL GERMAN LLM FOR ADVANCED SUMMARIZATION (This is the key change)
# We now use the absolute path to ensure the model is always found.
LOCAL_LLM_PATH = str(MODEL_DIR / "em_german_leo_mistral-Q4_K_M.gguf")


# --- Analysis & Classification Defaults ---
DEFAULT_SESSION_GAP = 10
DEFAULT_CONTEXT_WINDOW = 3
MAX_SEQUENCE_LENGTH = 512

# D. RAG THRESHOLD
ANALYSIS_RELEVANCE_THRESHOLD = 0.85
