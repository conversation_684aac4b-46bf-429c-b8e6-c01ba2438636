from fastapi import APIRouter, Form, HTTPException
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from pathlib import Path
from datetime import datetime

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.utils.logger import logger
from app.utils.output_utils import results_to_csv_string, results_to_jsonl_string
from app.config import DEFAULT_SESSION_GAP, DEFAULT_CONTEXT_WINDOW, MAX_SEQUENCE_LENGTH

router = APIRouter()

# --- Add this helper for datetime serialization ---
def clean_for_json(obj):
    if isinstance(obj, dict):
        return {k: clean_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [clean_for_json(i) for i in obj]
    elif isinstance(obj, datetime):
        return obj.isoformat()
    else:
        return obj

@router.post("/classify-folder")
async def classify_folder_api(
    folder_path: str = Form(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
    explain: bool = Form(False),              
):
    folder = Path(folder_path)
    if not folder.exists() or not folder.is_dir():
        raise HTTPException(status_code=400, detail="Invalid folder path")

    logger.info(f"Classifying folder: {folder.resolve()}")

    structured_files = []
    text_files = []
    errors = []
    file_outputs = {}

    # ─────────────────── iterate over every file ────────────────────
    for file_path in folder.glob("*"):
        if not file_path.is_file():
            continue

        try:
            # Plain text files
            if file_path.suffix.lower() == ".txt":
                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                    lines = [ln.strip() for ln in f if ln.strip()]

                results = []
                if context_aware:
                    chunks = []
                    for i in range(len(lines)):
                        prev = lines[i - 1] if i > 0 else ""
                        curr = lines[i]
                        next_ = lines[i + 1] if i + 1 < len(lines) else ""
                        chunks.append(f"{prev} [SEP] {curr} [SEP] {next_}")
                    classified = classifier.classify_text("\n".join(chunks), context_aware=False)

                    for i, res in enumerate(classified):
                        res.update(
                            {
                                "chunk_id": i,
                                "file": file_path.name,
                                #"line_number": f"{(i * MAX_SEQUENCE_LENGTH) + 1}+",
                                "text": chunks[i][:150],
                            }
                        )
                        if explain:
                            res["explanation"] = classifier.explain_with_interpret(
                                chunks[i], target_index=1
                            )
                        results.append(res)

                else:
                    # no context window line by line
                    for i, line in enumerate(lines):
                        res_list = classifier.classify_text(line, context_aware=False)
                        if isinstance(res_list, dict):
                            res_list = [res_list]

                        for r in res_list:
                            r.update(
                                {
                                    "line_number": i + 1,
                                    "text": line,
                                    "file": file_path.name,
                                }
                            )
                            if explain:
                                r["explanation"] = classifier.explain_with_interpret(
                                    line, target_index=1
                                )
                            results.append(r)

                # KPI for this txt file
                relevant_found = sum(1 for r in results if r["prediction"] == "Relevant")
                txt_stats = {
                    "lines_scanned": len(results),
                    "relevant_found": relevant_found,
                    "hit_rate": round(relevant_found / max(len(results), 1), 4),
                }

                # strings for download buttons
                file_outputs[file_path.name] = {
                    "csv_string": results_to_csv_string(results) or "",
                    "jsonl_string": results_to_jsonl_string(results) or "",
                    "results": results,
                    "stats": txt_stats,                       # NEW
                }

                text_files.append(
                    {
                        "file": file_path.name,
                        "relevant_found": relevant_found,
                        "total": len(results),
                        "stats": txt_stats,                   # NEW
                    }
                )

            # ───────────── STRUCTURED (xlsx / csv / db / sqlite) ─────────────
            elif file_path.suffix.lower() in [".xlsx", ".csv", ".ods", ".db", ".sqlite"]:
                result = BaseProcessor(
                    session_gap_minutes=session_gap_minutes,
                    context_window_size=context_window_size,
                ).detect_and_process(str(file_path), context_aware=context_aware)

                file_outputs[file_path.name] = {
                    "csv_string": result.get("csv_string", ""),
                    "jsonl_string": result.get("jsonl_string", ""),
                    "results": result.get("sessions", []),
                    "stats": result.get("stats", {}),
                }

                structured_files.append(
                    {
                        "file": file_path.name,
                        "platform": result.get("platform", "unknown"),
                        "stats": result.get("stats", {}),
                    }
                )

            else:
                errors.append(f"{file_path.name}: Unsupported file type")

        except Exception as e:
            logger.error(f"Failed to classify {file_path.name}: {e}")
            errors.append(f"{file_path.name}: {str(e)}")

    logger.info(
        f"Classification complete. Processed {len(text_files) + len(structured_files)} files."
    )

    # ─────────────────────── final JSON response ───────────────────────
    return JSONResponse(
        clean_for_json(
            {
                "status": "done",
                "output_folder": str(folder.resolve()),
                "text_files": text_files,
                "structured_files": structured_files,
                "file_outputs": file_outputs,
                "errors": errors,
            }
        )
    )
