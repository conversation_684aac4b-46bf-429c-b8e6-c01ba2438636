"""
Unit tests for WhatsApp parsers
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from pathlib import Path
from datetime import datetime, timezone
import sqlite3

from app.platform.whatsapp_parser import WhatsAppParser
from app.platform.whatsapp_db_parser import WhatsAppDBParser


class TestWhatsAppParser:
    """Test cases for WhatsAppParser"""
    
    def test_init(self):
        """Test WhatsAppParser initialization"""
        parser = WhatsAppParser()
        assert parser.valid_text_columns is not None
        assert parser.valid_time_columns is not None
        assert parser.valid_sender_columns is not None
        assert parser.valid_chat_columns is not None
    
    def test_detect_platform_files_xlsx(self, temp_dir):
        """Test detection of XLSX files"""
        # Create test files
        xlsx_file = temp_dir / "test.xlsx"
        xlsx_file.touch()
        txt_file = temp_dir / "test.txt"
        txt_file.touch()
        
        result = WhatsAppParser.detect_platform_files(temp_dir)
        
        assert "whatsapp_exports" in result
        assert len(result["whatsapp_exports"]) == 1
        assert result["whatsapp_exports"][0].name == "test.xlsx"
    
    def test_detect_platform_files_db(self, temp_dir):
        """Test detection of database files"""
        db_file = temp_dir / "msgstore.db"
        db_file.touch()
        
        result = WhatsAppParser.detect_platform_files(temp_dir)
        
        assert "whatsapp_databases" in result
        assert len(result["whatsapp_databases"]) == 1
        assert result["whatsapp_databases"][0].name == "msgstore.db"
    
    def test_is_valid_export_file_valid(self, sample_whatsapp_xlsx):
        """Test validation of valid WhatsApp export file"""
        result = WhatsAppParser.is_valid_export_file(sample_whatsapp_xlsx)
        assert result is True
    
    def test_is_valid_export_file_invalid(self, temp_dir):
        """Test validation of invalid export file"""
        # Create invalid file
        invalid_data = {"col1": ["data1"], "col2": ["data2"]}
        df = pd.DataFrame(invalid_data)
        file_path = temp_dir / "invalid.xlsx"
        df.to_excel(file_path, index=False)
        
        result = WhatsAppParser.is_valid_export_file(file_path)
        assert result is False
    
    def test_is_valid_export_file_nonexistent(self):
        """Test validation of non-existent file"""
        result = WhatsAppParser.is_valid_export_file(Path("nonexistent.xlsx"))
        assert result is False
    
    @patch('app.platform.whatsapp_parser.pd.read_excel')
    def test_parse_file_xlsx_success(self, mock_read_excel):
        """Test successful parsing of XLSX file"""
        # Mock pandas DataFrame
        mock_df = pd.DataFrame({
            "zeit": ["2024-01-01 10:00:00", "2024-01-01 10:05:00"],
            "nachricht": ["Message 1", "Message 2"],
            "von": ["User1", "User2"],
            "gruppenname": ["TestGroup", "TestGroup"]
        })
        mock_read_excel.return_value = mock_df
        
        parser = WhatsAppParser()
        result = parser.parse_file(Path("test.xlsx"))
        
        assert len(result) == 2
        assert result[0]["text"] == "Message 1"
        assert result[0]["sender"] == "User1"
        assert result[0]["chat_id"] == "TestGroup"
        assert result[0]["platform"] == "whatsapp"
    
    def test_normalize_message(self):
        """Test message normalization"""
        parser = WhatsAppParser()
        
        raw_message = {
            "zeit": "2024-01-01 10:00:00",
            "nachricht": "Test message",
            "von": "TestUser",
            "gruppenname": "TestGroup"
        }
        
        result = parser.normalize_message(raw_message)
        
        assert isinstance(result["timestamp"], datetime)
        assert result["text"] == "Test message"
        assert result["sender"] == "TestUser"
        assert result["chat_id"] == "TestGroup"
        assert result["platform"] == "whatsapp"
    
    def test_clean_message_basic(self):
        """Test basic message cleaning"""
        parser = WhatsAppParser()
        
        # Test normal message
        result = parser._clean_message("Hello world")
        assert result == "Hello world"
        
        # Test empty message
        result = parser._clean_message("")
        assert result == ""
        
        # Test None message
        result = parser._clean_message(None)
        assert result == ""
    
    def test_clean_message_media_omitted(self):
        """Test cleaning of media omitted messages"""
        parser = WhatsAppParser()
        
        result = parser._clean_message("<Media omitted>")
        assert result == ""
        
        result = parser._clean_message("image omitted")
        assert result == ""
    
    def test_parse_timestamp_valid(self):
        """Test parsing valid timestamps"""
        parser = WhatsAppParser()
        
        # Test standard format
        result = parser._parse_timestamp("2024-01-01 10:00:00")
        assert isinstance(result, datetime)
        assert result.year == 2024
        assert result.month == 1
        assert result.day == 1
    
    def test_parse_timestamp_invalid(self):
        """Test parsing invalid timestamps"""
        parser = WhatsAppParser()
        
        result = parser._parse_timestamp("invalid-timestamp")
        assert result is None
        
        result = parser._parse_timestamp("")
        assert result is None
        
        result = parser._parse_timestamp(None)
        assert result is None


class TestWhatsAppDBParser:
    """Test cases for WhatsAppDBParser"""
    
    def test_init(self):
        """Test WhatsAppDBParser initialization"""
        parser = WhatsAppDBParser()
        assert hasattr(parser, '_CLEAN_PATTERNS')
    
    def test_parse_file_success(self, sample_whatsapp_db):
        """Test successful parsing of WhatsApp database"""
        parser = WhatsAppDBParser()
        result = parser.parse_file(sample_whatsapp_db)
        
        assert len(result) >= 0  # Should return some messages
        if result:  # If messages were found
            assert "text" in result[0]
            assert "timestamp" in result[0]
            assert "sender" in result[0]
            assert "platform" in result[0]
            assert result[0]["platform"] == "whatsapp"
    
    def test_parse_file_nonexistent(self):
        """Test parsing non-existent database file"""
        parser = WhatsAppDBParser()
        
        with pytest.raises(Exception):  # Should raise some kind of database error
            parser.parse_file(Path("nonexistent.db"))
    
    def test_detect_msg_table(self, sample_whatsapp_db):
        """Test message table detection"""
        parser = WhatsAppDBParser()
        
        import sqlite3
        conn = sqlite3.connect(sample_whatsapp_db)
        
        table_name = parser._detect_msg_table(conn)
        assert table_name in ["message", "messages", "MESSAGE", "MESSAGES"]
        
        conn.close()
    
    def test_clean_message_patterns(self):
        """Test message cleaning with various patterns"""
        parser = WhatsAppDBParser()
        
        # Test normal message
        result = parser._clean_message("Normal message")
        assert result == "Normal message"
        
        # Test empty message
        result = parser._clean_message("")
        assert result == ""
        
        # Test None
        result = parser._clean_message(None)
        assert result == ""
    
    def test_convert_timestamp_microseconds(self):
        """Test timestamp conversion from microseconds"""
        parser = WhatsAppDBParser()
        
        # Test microseconds (typical WhatsApp format)
        microseconds = 1704110400000000  # 2024-01-01 10:00:00 UTC in microseconds
        result = parser._convert_ts(microseconds)
        
        assert isinstance(result, datetime)
        assert result.year == 2024
    
    def test_convert_timestamp_milliseconds(self):
        """Test timestamp conversion from milliseconds"""
        parser = WhatsAppDBParser()
        
        # Test milliseconds
        milliseconds = 1704110400000  # 2024-01-01 10:00:00 UTC in milliseconds
        result = parser._convert_ts(milliseconds)
        
        assert isinstance(result, datetime)
        assert result.year == 2024
    
    def test_convert_timestamp_invalid(self):
        """Test timestamp conversion with invalid values"""
        parser = WhatsAppDBParser()
        
        # Test None
        result = parser._convert_ts(None)
        assert result is None
        
        # Test zero
        result = parser._convert_ts(0)
        assert result is None
        
        # Test negative
        result = parser._convert_ts(-1)
        assert result is None
    
    def test_normalize_message(self):
        """Test message normalization for database parser"""
        parser = WhatsAppDBParser()
        
        # This is an abstract method, so we test that it exists
        assert hasattr(parser, 'normalize_message')
        
        # The actual implementation would depend on the specific format
        # For now, we just test that the method exists
