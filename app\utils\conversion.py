# app/utils/conversion.py

import numpy as np
import pandas as pd

def to_builtin_type(obj):
    # Scalars
    if isinstance(obj, (np.integer,)):
        return int(obj)
    if isinstance(obj, (np.floating,)):
        return float(obj)
    if isinstance(obj, (np.bool_,)):
        return bool(obj)
    if isinstance(obj, (pd.Timestamp,)):
        return obj.isoformat()
    # Arrays
    if isinstance(obj, (np.ndarray,)):
        return obj.tolist()
    if isinstance(obj, pd.Series):
        return obj.apply(to_builtin_type).to_dict()
    if isinstance(obj, pd.DataFrame):
        return obj.applymap(to_builtin_type).to_dict(orient='records')
    # Only test pd.NA and pd.isna for scalars (not lists/arrays)
    if obj is pd.NA:
        return None
    if not isinstance(obj, (list, tuple, set, dict, np.ndarray, pd.Series, pd.DataFrame)):
        try:
            if pd.isna(obj):
                return None
        except Exception:
            pass
    # Recursion for containers
    if isinstance(obj, dict):
        return {to_builtin_type(k): to_builtin_type(v) for k, v in obj.items()}
    if isinstance(obj, (list, tuple, set)):
        t = type(obj)
        return t([to_builtin_type(i) for i in obj])
    return obj
