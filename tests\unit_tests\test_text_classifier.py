"""
Essential unit tests for TextClassifier and EnsembleClassifier
"""
import pytest
from unittest.mock import Mock, patch
from pathlib import Path

from app.core.text_classifier import TextClassifier
from app.core.ensemble_classifier import EnsembleClassifier


class TestTextClassifier:
    """Essential tests for TextClassifier"""

    def test_init(self):
        """Test initialization"""
        classifier = TextClassifier("test/path")
        assert classifier.model_path == Path("test/path")
        assert classifier.labels == ["Non-Relevant", "Relevant"]

    def test_init_empty_path_fails(self):
        """Test empty path raises error"""
        with pytest.raises(ValueError):
            TextClassifier("")

    def test_classify_text_empty_returns_empty(self):
        """Test empty input returns empty list"""
        classifier = TextClassifier("test/path")
        assert classifier.classify_text("") == []
        assert classifier.classify_text("   ") == []

    def test_classify_standard_inputs_empty(self):
        """Test empty input returns empty list"""
        classifier = TextClassifier("test/path")
        assert classifier.classify_standard_inputs([]) == []


class TestEnsembleClassifier:
    """Essential tests for EnsembleClassifier"""

    @patch('app.core.ensemble_classifier.TextClassifier')
    def test_init(self, mock_classifier):
        """Test initialization with multiple models"""
        configs = {"model1": "path1", "model2": "path2"}
        ensemble = EnsembleClassifier(configs)

        assert len(ensemble.members) == 2
        assert ensemble.model_names == ["model1", "model2"]

    def test_classify_text_empty_results(self):
        """Test empty results handling"""
        ensemble = EnsembleClassifier({"model1": "path1"})
        ensemble.members = [Mock()]
        ensemble.members[0].classify_text.return_value = []

        result = ensemble.classify_text("test")
        assert result == []

    def test_classify_text_merges_predictions(self):
        """Test prediction merging from multiple models"""
        mock1 = Mock()
        mock2 = Mock()
        mock1.classify_text.return_value = [{"text": "test", "prediction": "Relevant", "confidence": 0.9}]
        mock2.classify_text.return_value = [{"text": "test", "prediction": "Non-Relevant", "confidence": 0.7}]

        ensemble = EnsembleClassifier({"model1": "path1", "model2": "path2"})
        ensemble.members = [mock1, mock2]
        ensemble.model_names = ["model1", "model2"]

        result = ensemble.classify_text("test")

        assert len(result) == 1
        assert result[0]["model1_prediction"] == "Relevant"
        assert result[0]["model2_prediction"] == "Non-Relevant"
        assert "prediction" not in result[0]  # Generic key removed
