from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder           # ← serialises datetime
from pathlib import Path
import tempfile, shutil
from typing import List, Dict, Any

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.utils.logger import logger
from app.utils.output_utils import (
    flatten_sessions,
    results_to_csv_string,
    results_to_jsonl_string,
)
from app.config import (
    DEFAULT_SESSION_GAP,
    DEFAULT_CONTEXT_WINDOW,
    MAX_SEQUENCE_LENGTH,
)

router = APIRouter()

# ────────────────────────────────────────────────────────────
@router.post("/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
    explain: bool = Form(False),
):
    temp_dir = Path(tempfile.mkdtemp())

    try:
        file_path = temp_dir / file.filename
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)

        # ───────────── plain .txt ────────────────────────────
        if file_path.suffix.lower() == ".txt":
            results: List[Dict[str, Any]] = []
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                lines = [line.strip() for line in f if line.strip()]

            if context_aware:
                classified = classifier.classify_text(
                    "\n".join(lines), context_aware=True
                )
                for i, result in enumerate(classified):
                    result.update(
                        {
                            "chunk_id": i,
                            "file": file.filename,
                            #"line": f"{(i * MAX_SEQUENCE_LENGTH) + 1}+",
                            "text": result.get("input_snippet", "[context chunk]"),
                        }
                    )
                    results.append(result)
            else:
                for i, line in enumerate(lines):
                    res = classifier.classify_text(line, context_aware=False)
                    if isinstance(res, dict):
                        res = [res]
                    for r in res:
                        r.update({"line": i + 1, "text": line, "file": file.filename})
                        if explain:
                            r["explanation"] = classifier.explain_with_interpret(
                                line, target_index=1
                            )
                        results.append(r)

            csv_str = results_to_csv_string(results)
            jsonl_str = results_to_jsonl_string(results)

            relevant_found = sum(1 for r in results if r["prediction"] == "Relevant")
            stats = {
                "lines_scanned": len(results),
                "relevant_found": relevant_found,
                "hit_rate": round(relevant_found / max(len(results), 1), 4),
            }

            return JSONResponse(
                jsonable_encoder(               # ← wrap once
                    {
                        "status": "completed",
                        "file_type": "txt",
                        "context_aware": context_aware,
                        "stats": stats,
                        "results": results,
                        "csv_string": csv_str,
                        "jsonl_string": jsonl_str,
                        "filename_prefix": file.filename.split(".")[0],
                    }
                )
            )

        # ───────────── structured files (.xlsx, .db, etc.) ──────────
        result = BaseProcessor(
            session_gap_minutes=session_gap_minutes,
            context_window_size=context_window_size,
        ).detect_and_process(str(temp_dir), context_aware=context_aware)

        flat_results = flatten_sessions(result.get("results", []))
        csv_str = results_to_csv_string(flat_results)
        jsonl_str = results_to_jsonl_string(flat_results)

        sessions = result.get("results", [])
        if not sessions or not any(s.get("messages") for s in sessions):
            sessions = [
                {"session_id": str(i), "messages": [msg]}
                for i, msg in enumerate(flat_results)
            ]

        return JSONResponse(
            jsonable_encoder(               # ← wrap once
                {
                    "status": "completed",
                    "file_type": "structured",
                    "platform": result.get("platform", "unknown"),
                    "stats": result.get("stats", {}),
                    "results": sessions,
                    "csv_string": csv_str,
                    "jsonl_string": jsonl_str,
                }
            )
        )

    except Exception as e:
        logger.error(f"File upload failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
