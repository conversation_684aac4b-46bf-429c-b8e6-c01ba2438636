from fastapi import APIRouter, Form
from fastapi.responses import JSONResponse
from app.core.text_classifier import classifier
from app.services.analysis_service import analysis_service 

router = APIRouter()

@router.post("/classify-text")
async def classify_text(
    text: str = Form(...),
    context_aware: bool = Form(False),
    explain: bool = Form(False),
    summarize: bool = Form(False),  
):
    try:
        results = classifier.classify_text(text, context_aware=context_aware)
        
        # PATCH: Add 'line' and 'text' fields for frontend display
        for i, r in enumerate(results):
            if "line_number" in r:
                r["line"] = r.pop("line_number")
            if "input_snippet" in r:
                r["text"] = r.pop("input_snippet")

            if explain:
                # index 1 = "Relevant" class (0 = Non-Relevant)
                r["explanation"] = classifier.explain_with_interpret(
                    r["text"], target_index=1
                )

        output = {
            "status": "completed",
            "results": results,
        }

        # Optionally add RAG summary
        if summarize and results:
            rag_report = analysis_service.cluster_and_summarize(results)
            output["rag_report"] = rag_report

        return JSONResponse(output)

    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)
