["tests/unit_tests/test_integration.py::TestBaseProcessorIntegration::test_base_processor_txt_file_rejection", "tests/unit_tests/test_integration.py::TestBaseProcessorIntegration::test_base_processor_whatsapp_detection", "tests/unit_tests/test_integration.py::TestEndToEndProcessing::test_empty_file_handling", "tests/unit_tests/test_integration.py::TestEndToEndProcessing::test_end_to_end_whatsapp_xlsx_processing", "tests/unit_tests/test_integration.py::TestEndToEndProcessing::test_error_propagation_through_pipeline", "tests/unit_tests/test_integration.py::TestWhatsAppProcessingPipeline::test_complete_whatsapp_processing_pipeline", "tests/unit_tests/test_integration.py::TestWhatsAppProcessingPipeline::test_context_aware_processing", "tests/unit_tests/test_integration.py::TestWhatsAppProcessingPipeline::test_sessionizer_integration", "tests/unit_tests/test_services.py::TestAnalysisService::test_cluster_and_summarize_insufficient_messages", "tests/unit_tests/test_services.py::TestAnalysisService::test_ensure_models_loaded_missing_file", "tests/unit_tests/test_services.py::TestAnalysisService::test_ensure_models_loaded_success", "tests/unit_tests/test_services.py::TestAnalysisService::test_filter_and_deduplicate_empty", "tests/unit_tests/test_services.py::TestAnalysisService::test_filter_and_deduplicate_no_relevant", "tests/unit_tests/test_services.py::TestAnalysisService::test_filter_and_deduplicate_with_relevant", "tests/unit_tests/test_services.py::TestAnalysisService::test_health_check_error", "tests/unit_tests/test_services.py::TestAnalysisService::test_health_check_uninitialized", "tests/unit_tests/test_services.py::TestAnalysisService::test_init", "tests/unit_tests/test_services.py::TestBaseProcessor::test_detect_and_process_nonexistent_path", "tests/unit_tests/test_services.py::TestBaseProcessor::test_detect_and_process_txt_file", "tests/unit_tests/test_services.py::TestBaseProcessor::test_detect_and_process_whatsapp_failure", "tests/unit_tests/test_services.py::TestBaseProcessor::test_detect_and_process_whatsapp_success", "tests/unit_tests/test_services.py::TestBaseProcessor::test_init", "tests/unit_tests/test_services.py::TestSessionizer::test_init", "tests/unit_tests/test_services.py::TestSessionizer::test_sessionize_empty_messages", "tests/unit_tests/test_services.py::TestSessionizer::test_sessionize_multiple_sessions", "tests/unit_tests/test_services.py::TestSessionizer::test_sessionize_preserves_message_data", "tests/unit_tests/test_services.py::TestSessionizer::test_sessionize_single_message", "tests/unit_tests/test_services.py::TestWhatsAppProcessor::test_init", "tests/unit_tests/test_services.py::TestWhatsAppProcessor::test_process_whatsapp_data_no_files", "tests/unit_tests/test_services.py::TestWhatsAppProcessor::test_process_whatsapp_data_success", "tests/unit_tests/test_text_classifier.py::TestEnsembleClassifier::test_classify_standard_inputs", "tests/unit_tests/test_text_classifier.py::TestEnsembleClassifier::test_classify_text_empty_results", "tests/unit_tests/test_text_classifier.py::TestEnsembleClassifier::test_classify_text_merge_results", "tests/unit_tests/test_text_classifier.py::TestEnsembleClassifier::test_init", "tests/unit_tests/test_text_classifier.py::TestEnsembleClassifier::test_load_model", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_classify_standard_inputs_empty", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_classify_standard_inputs_normal", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_classify_text_context_aware_mode", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_classify_text_empty_input", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_classify_text_normal_mode", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_init_with_empty_path", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_init_with_valid_path", "tests/unit_tests/test_text_classifier.py::TestTextClassifier::test_load_model_success", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_clean_message_patterns", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_convert_timestamp_invalid", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_convert_timestamp_microseconds", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_convert_timestamp_milliseconds", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_detect_msg_table", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_init", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_normalize_message", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_parse_file_nonexistent", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppDBParser::test_parse_file_success", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_clean_message_basic", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_clean_message_media_omitted", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_detect_platform_files_db", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_detect_platform_files_xlsx", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_init", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_is_valid_export_file_invalid", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_is_valid_export_file_nonexistent", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_is_valid_export_file_valid", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_normalize_message", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_parse_file_xlsx_success", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_parse_timestamp_invalid", "tests/unit_tests/test_whatsapp_parser.py::TestWhatsAppParser::test_parse_timestamp_valid"]