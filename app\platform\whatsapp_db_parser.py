"""
app/platform/whatsapp_db_parser.py
----------------------------------
Parse a *decrypted* WhatsApp message store (msgstore.db / messages.db) and
return the same normalised message-dicts produced by WhatsAppParser export
loader.  Designed to cope with several schema vintages (Android/iOS, pre-/post-
2022) it autodetects table / column names and silently skips media rows.
"""

from __future__ import annotations

import re
import sqlite3
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List

from ..utils.logger import logger
from .base_parser import BaseSocialMediaParser


class WhatsAppDBParser(BaseSocialMediaParser):
    TABLE_CANDIDATES  = ["message", "messages", "ZMESSAGE"]

    TEXT_COLS         = ["text_data", "data", "ZTEXT"]
    CHAT_FK_COLS      = ["chat_row_id", "key_remote_jid", "ZCHAT"]
    SENDER_FK_COLS    = ["sender_jid_row_id", "remote_resource", "ZSENDER"]
    FROM_ME_COLS      = ["from_me", "key_from_me"]

    _CLEAN_PATTERNS   = [
        r"<Media omitted>", r"message was deleted", r"missed voice call",
        r"image omitted", r"video omitted", r"audio omitted"
    ]

    @classmethod
    def detect_platform_files(cls, input_path: Path):
        return {"exports": [], "databases": [Path(input_path)], "other": []}

    def normalize_message(self, message: Dict[str, Any]):
        return message

    def parse_file(self, file_path: Path) -> List[Dict[str, Any]]:
        logger.info("Parsing WhatsApp DB '%s'", file_path.name)

        with sqlite3.connect(file_path) as conn:
            # --- Build lookups ---
            logger.info("Building chat and sender lookups...")

            # 1. Chat lookup: {chat_row_id: (jid_row_id, group_name)}
            chat_lu = {}
            for row in conn.execute("SELECT _id, jid_row_id, subject FROM chat"):
                chat_id, jid_row_id, subject = row
                chat_lu[chat_id] = {"jid_row_id": jid_row_id, "group_name": subject}

            # 2. JID lookup: {_id: {user, raw_string}}
            jid_lu = {}
            for row in conn.execute("SELECT _id, user, raw_string FROM jid"):
                _id, user, raw_string = row
                jid_lu[_id] = {"user": user, "raw_string": raw_string}

            logger.info(f"Loaded {len(chat_lu)} chat entries and {len(jid_lu)} jid entries.")

            # 3. Read all messages
            msg_rows = conn.execute("""
                SELECT _id, chat_row_id, sender_jid_row_id, from_me, text_data, timestamp
                FROM message
                WHERE text_data IS NOT NULL AND text_data != ''
                ORDER BY timestamp ASC
            """).fetchall()

            logger.info(f"Loaded {len(msg_rows)} messages with non-empty text.")

        msgs = []
        for idx, (msg_id, chat_fk, sender_fk, from_me, text, ts) in enumerate(msg_rows):
            # --- Chat Info ---
            chat_info = chat_lu.get(chat_fk, {})
            group_name = str(chat_info.get("group_name", "")) if chat_info.get("group_name") else ""
            chat_jid_info = jid_lu.get(chat_info.get("jid_row_id", -1), {})
            chat_jid = str(chat_jid_info.get("raw_string", "")) if chat_jid_info.get("raw_string") else str(chat_fk)
            chat_number = str(chat_jid_info.get("user", "")) if chat_jid_info.get("user") else chat_jid

            if group_name:
                chat_id = group_name
                chat_type = "group"
            else:
                chat_id = chat_number
                chat_type = "single"

            # --- Sender Info ---
            if from_me:
                sender = "me"
            else:
                sender_info = jid_lu.get(sender_fk, {})
                sender_user = str(sender_info.get("user", "")) if sender_info.get("user") else ""
                sender_raw = str(sender_info.get("raw_string", "")) if sender_info.get("raw_string") else ""
                sender = sender_user or sender_raw or str(sender_fk)

            logger.info(f"[{idx}] chat_fk={chat_fk} -> chat_id='{chat_id}' (type={chat_type}) | "
                        f"sender_fk={sender_fk} -> sender='{sender}' | msg_id={msg_id}")

            # Clean and ensure text is a string
            clean_text = self._clean_message(text) if text else ""

            msgs.append({
                "timestamp": self._convert_ts(ts),
                "text": clean_text,
                "chat_id": chat_id,
                "chat_type": chat_type,
                "sender": sender,
                "platform": "whatsapp",
            })

        logger.info("Parsed %d text rows from DB '%s'", len(msgs), file_path.name)
        return msgs

    def _detect_msg_table(self, conn) -> str:
        tables = {r[0].lower() for r in conn.execute(
            "SELECT name FROM sqlite_master WHERE type='table'")}
        logger.info("Available tables in DB: %s", tables)
        for cand in self.TABLE_CANDIDATES:
            if cand in tables:
                return cand
        raise RuntimeError("No recognised WhatsApp message table found")

    def _detect_columns(self, conn, table) -> Dict[str, str]:
        cols = {c[1].lower() for c in conn.execute(f"PRAGMA table_info({table})")}
        logger.info("Available columns in table '%s': %s", table, cols)
        def pick(cands: List[str]) -> str:
            for c in cands:
                if c in cols:
                    return c
            raise RuntimeError(f"Missing expected column in {table}")
        return {
            "text":      pick(self.TEXT_COLS),
            "chat_fk":   pick(self.CHAT_FK_COLS),
            "sender_fk": pick(self.SENDER_FK_COLS),
            "from_me":   pick(self.FROM_ME_COLS),
        }

    def _build_lookups(self, conn):
        # chat_view (newer Android)  / chat (older iOS)  / ZWACHATS (iOS CoreData)
        chat_lu, sender_lu = {}, {}
        for sql in (
            "SELECT _id, jid FROM chat_view",
            "SELECT _id, jid FROM chat",
            "SELECT Z_PK, ZJID FROM ZWACHATS",
        ):
            try:
                chat_lu = dict(conn.execute(sql))
                if chat_lu:
                    logger.info("Loaded chat lookup with SQL: %s", sql)
                    break
            except sqlite3.Error:
                continue

        for sql in (
            "SELECT _id, raw_string FROM jid",
            "SELECT Z_PK, ZCONTACTJID FROM ZWAJIDS",
        ):
            try:
                sender_lu = dict(conn.execute(sql))
                if sender_lu:
                    logger.info("Loaded sender lookup with SQL: %s", sql)
                    break
            except sqlite3.Error:
                continue

        return chat_lu, sender_lu

    def _build_group_name_lookup(self, conn) -> Dict[str, str]:
        group_lu = {}
        queries = [
            "SELECT jid, subject FROM group_participants",
            "SELECT jid, subject FROM group_info",
            "SELECT jid, display_name FROM chat",
        ]
        for q in queries:
            try:
                for jid, subject in conn.execute(q):
                    if jid and subject:
                        group_lu[str(jid)] = str(subject)
                logger.info("Loaded group_name_lu with SQL: %s", q)
            except sqlite3.Error:
                continue
        return group_lu

    def _build_contact_name_lookup(self, conn) -> Dict[str, str]:
        contact_lu = {}
        queries = [
            "SELECT jid, display_name FROM wa_contacts",
            "SELECT jid, given_name FROM wa_contacts",
            "SELECT jid, raw_string FROM jid",
        ]
        for q in queries:
            try:
                for jid, name in conn.execute(q):
                    if jid and name:
                        contact_lu[str(jid)] = str(name)
                logger.info("Loaded contact_name_lu with SQL: %s", q)
            except sqlite3.Error:
                continue
        return contact_lu

    @classmethod
    def _clean_message(cls, text):
        if not text:
            return ""
        # Ensure text is a string and handle potential integer values
        try:
            t = str(text).strip()
            # Skip cleaning if text appears to be just a number (likely not actual message text)
            if t.isdigit():
                return t
            for p in cls._CLEAN_PATTERNS:
                if re.search(p, t, re.IGNORECASE):
                    return ""
            return t
        except (TypeError, AttributeError):
            # If there's any issue with string conversion, return empty string
            return ""

    @staticmethod
    def _convert_ts(ts):
        """Accept µs / ms / s since epoch → timezone-aware datetime or None."""
        if ts is None:
            return None
        ts = int(ts)
        while ts > 32_503_680_000:          # shrink µs→ms→s
            ts //= 1000
        if ts <= 0:
            return None
        try:
            return datetime.fromtimestamp(ts, tz=timezone.utc)
        except (OSError, OverflowError, ValueError):
            return None

    @staticmethod
    def _resolve_sender(raw, from_me):
        if from_me:
            return "me"
        return (str(raw).split("@")[0] if raw else "unknown")
