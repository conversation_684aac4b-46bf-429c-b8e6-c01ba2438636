"""
Unit tests for API routes
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import UploadFile
import tempfile
import io
from pathlib import Path

from app.main import app


class TestAPIRoutes:
    """Test cases for API routes"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_classifier(self):
        """Mock classifier for testing"""
        with patch('app.routes.raw_text.classifier') as mock:
            mock.classify_text.return_value = [
                {
                    "text": "test message",
                    "line_number": 1,
                    "prediction": "Relevant",
                    "confidence": 0.85
                }
            ]
            yield mock
    
    @pytest.fixture
    def mock_analysis_service(self):
        """Mock analysis service for testing"""
        with patch('app.routes.raw_text.analysis_service') as mock:
            mock.health_check.return_value = {
                "status": "healthy",
                "model_file_exists": True,
                "models_loaded": True,
                "model_path": "/path/to/model"
            }
            yield mock


class TestRawTextRoutes(TestAPIRoutes):
    """Test cases for raw text classification routes"""
    
    def test_classify_text_success(self, client, mock_classifier):
        """Test successful text classification"""
        response = client.post(
            "/api/classify-text",
            data={
                "text": "Test message for classification",
                "context_aware": False,
                "explain": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "results" in data
        assert len(data["results"]) == 1
        assert data["results"][0]["prediction"] == "Relevant"
    
    def test_classify_text_context_aware(self, client, mock_classifier):
        """Test text classification with context awareness"""
        response = client.post(
            "/api/classify-text",
            data={
                "text": "Line 1\nLine 2\nLine 3",
                "context_aware": True,
                "explain": False
            }
        )
        
        assert response.status_code == 200
        mock_classifier.classify_text.assert_called_once_with(
            "Line 1\nLine 2\nLine 3", 
            context_aware=True
        )
    
    def test_classify_text_with_explanation(self, client, mock_classifier):
        """Test text classification with explanation"""
        mock_classifier.explain_with_interpret.return_value = "explanation data"
        
        response = client.post(
            "/api/classify-text",
            data={
                "text": "Test message",
                "context_aware": False,
                "explain": True
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "explanation" in data["results"][0]
    
    def test_classify_text_error_handling(self, client):
        """Test error handling in text classification"""
        with patch('app.routes.raw_text.classifier') as mock_classifier:
            mock_classifier.classify_text.side_effect = Exception("Classification failed")
            
            response = client.post(
                "/api/classify-text",
                data={"text": "Test message", "context_aware": False, "explain": False}
            )
            
            assert response.status_code == 500
            data = response.json()
            assert data["status"] == "error"
            assert "Classification failed" in data["message"]
    
    def test_analysis_service_health_check_healthy(self, client, mock_analysis_service):
        """Test analysis service health check when healthy"""
        response = client.get("/api/analysis-service-health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["model_file_exists"] is True
        assert data["models_loaded"] is True
    
    def test_analysis_service_health_check_unhealthy(self, client, mock_analysis_service):
        """Test analysis service health check when unhealthy"""
        mock_analysis_service.health_check.return_value = {
            "status": "unhealthy",
            "error": "Model file not found"
        }
        
        response = client.get("/api/analysis-service-health")
        
        assert response.status_code == 500
        data = response.json()
        assert data["status"] == "unhealthy"
        assert "error" in data
    
    def test_analysis_service_health_check_exception(self, client):
        """Test analysis service health check with exception"""
        with patch('app.routes.raw_text.analysis_service') as mock_service:
            mock_service.health_check.side_effect = Exception("Service error")
            
            response = client.get("/api/analysis-service-health")
            
            assert response.status_code == 500
            data = response.json()
            assert data["status"] == "error"
            assert "Failed to check analysis service" in data["message"]


class TestFileUploadRoutes(TestAPIRoutes):
    """Test cases for file upload routes"""
    
    @patch('app.routes.file_upload.classifier')
    def test_upload_txt_file_success(self, mock_classifier, client):
        """Test successful .txt file upload"""
        mock_classifier.classify_text.return_value = [
            {"text": "test line", "prediction": "Relevant", "confidence": 0.8}
        ]
        
        # Create test file content
        file_content = "Test line 1\nTest line 2"
        file_data = io.BytesIO(file_content.encode())
        
        response = client.post(
            "/api/upload-file",
            files={"file": ("test.txt", file_data, "text/plain")},
            data={
                "session_gap_minutes": 10,
                "context_window_size": 3,
                "context_aware": False,
                "explain": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert data["file_type"] == "txt"
        assert "results" in data
    
    @patch('app.routes.file_upload.BaseProcessor')
    def test_upload_structured_file_success(self, mock_base_processor, client):
        """Test successful structured file upload"""
        # Setup mock
        mock_processor_instance = Mock()
        mock_processor_instance.detect_and_process.return_value = {
            "results": [{"text": "test", "prediction": "Relevant"}],
            "stats": {"messages": 1},
            "platform": "whatsapp"
        }
        mock_base_processor.return_value = mock_processor_instance
        
        # Create test file
        file_data = io.BytesIO(b"test xlsx content")
        
        response = client.post(
            "/api/upload-file",
            files={"file": ("test.xlsx", file_data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")},
            data={
                "session_gap_minutes": 10,
                "context_window_size": 3,
                "context_aware": False,
                "explain": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "results" in data
        assert "stats" in data
    
    def test_upload_file_error_handling(self, client):
        """Test file upload error handling"""
        with patch('app.routes.file_upload.BaseProcessor') as mock_processor:
            mock_processor.return_value.detect_and_process.side_effect = Exception("Processing failed")
            
            file_data = io.BytesIO(b"test content")
            
            response = client.post(
                "/api/upload-file",
                files={"file": ("test.xlsx", file_data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")},
                data={
                    "session_gap_minutes": 10,
                    "context_window_size": 3,
                    "context_aware": False,
                    "explain": False
                }
            )
            
            assert response.status_code == 500
            data = response.json()
            assert data["status"] == "error"


class TestFolderPathRoutes(TestAPIRoutes):
    """Test cases for folder path classification routes"""
    
    @patch('app.routes.folder_path.classifier')
    @patch('app.routes.folder_path.BaseProcessor')
    def test_classify_folder_success(self, mock_base_processor, mock_classifier, client, temp_dir):
        """Test successful folder classification"""
        # Create test files
        txt_file = temp_dir / "test.txt"
        txt_file.write_text("Test content")
        
        xlsx_file = temp_dir / "test.xlsx"
        xlsx_file.touch()
        
        # Setup mocks
        mock_classifier.classify_text.return_value = [
            {"text": "test", "prediction": "Relevant", "confidence": 0.8}
        ]
        
        mock_processor_instance = Mock()
        mock_processor_instance.detect_and_process.return_value = {
            "results": [{"text": "test", "prediction": "Relevant"}],
            "stats": {"messages": 1}
        }
        mock_base_processor.return_value = mock_processor_instance
        
        response = client.post(
            "/api/classify-folder",
            data={
                "folder_path": str(temp_dir),
                "session_gap_minutes": 10,
                "context_window_size": 3,
                "context_aware": False,
                "explain": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert "file_outputs" in data
        assert "summary" in data
    
    def test_classify_folder_invalid_path(self, client):
        """Test folder classification with invalid path"""
        response = client.post(
            "/api/classify-folder",
            data={
                "folder_path": "/nonexistent/path",
                "session_gap_minutes": 10,
                "context_window_size": 3,
                "context_aware": False,
                "explain": False
            }
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "Invalid folder path" in data["detail"]
    
    @patch('app.routes.folder_path.classifier')
    def test_classify_folder_processing_error(self, mock_classifier, client, temp_dir):
        """Test folder classification with processing error"""
        # Create test file
        txt_file = temp_dir / "test.txt"
        txt_file.write_text("Test content")
        
        # Setup mock to raise exception
        mock_classifier.classify_text.side_effect = Exception("Processing failed")
        
        response = client.post(
            "/api/classify-folder",
            data={
                "folder_path": str(temp_dir),
                "session_gap_minutes": 10,
                "context_window_size": 3,
                "context_aware": False,
                "explain": False
            }
        )
        
        assert response.status_code == 200  # Should still return 200 but with errors
        data = response.json()
        assert data["status"] == "completed"
        assert "errors" in data
        assert len(data["errors"]) > 0
