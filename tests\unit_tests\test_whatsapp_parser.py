"""
Essential unit tests for WhatsApp parsers
"""
import pytest
from unittest.mock import patch
import pandas as pd
from pathlib import Path
from datetime import datetime

from app.platform.whatsapp_parser import WhatsAppParser
from app.platform.whatsapp_db_parser import WhatsAppDBParser


class TestWhatsAppParser:
    """Essential tests for WhatsAppParser"""

    def test_init(self):
        """Test initialization"""
        parser = WhatsAppParser()
        assert hasattr(parser, 'valid_text_columns')
        assert hasattr(parser, 'valid_time_columns')

    def test_detect_platform_files(self, temp_dir):
        """Test file detection"""
        xlsx_file = temp_dir / "test.xlsx"
        xlsx_file.touch()
        db_file = temp_dir / "msgstore.db"
        db_file.touch()

        result = WhatsAppParser.detect_platform_files(temp_dir)

        assert "whatsapp_exports" in result
        assert "whatsapp_databases" in result
        assert len(result["whatsapp_exports"]) == 1
        assert len(result["whatsapp_databases"]) == 1

    def test_is_valid_export_file_nonexistent(self):
        """Test validation of non-existent file"""
        result = WhatsAppParser.is_valid_export_file(Path("nonexistent.xlsx"))
        assert result is False

    @patch('app.platform.whatsapp_parser.pd.read_excel')
    def test_parse_file_success(self, mock_read_excel):
        """Test successful file parsing"""
        mock_df = pd.DataFrame({
            "zeit": ["2024-01-01 10:00:00"],
            "nachricht": ["Test message"],
            "von": ["User1"],
            "gruppenname": ["TestGroup"]
        })
        mock_read_excel.return_value = mock_df

        parser = WhatsAppParser()
        result = parser.parse_file(Path("test.xlsx"))

        assert len(result) == 1
        assert result[0]["text"] == "Test message"
        assert result[0]["platform"] == "whatsapp"

    def test_clean_message(self):
        """Test message cleaning"""
        parser = WhatsAppParser()

        assert parser._clean_message("Hello") == "Hello"
        assert parser._clean_message("") == ""
        assert parser._clean_message("<Media omitted>") == ""

    def test_parse_timestamp(self):
        """Test timestamp parsing"""
        parser = WhatsAppParser()

        result = parser._parse_timestamp("2024-01-01 10:00:00")
        assert isinstance(result, datetime)

        assert parser._parse_timestamp("invalid") is None


class TestWhatsAppDBParser:
    """Essential tests for WhatsAppDBParser"""

    def test_init(self):
        """Test initialization"""
        parser = WhatsAppDBParser()
        assert hasattr(parser, '_CLEAN_PATTERNS')

    def test_parse_file_nonexistent(self):
        """Test parsing non-existent file raises error"""
        parser = WhatsAppDBParser()
        with pytest.raises(Exception):
            parser.parse_file(Path("nonexistent.db"))

    def test_clean_message(self):
        """Test message cleaning"""
        parser = WhatsAppDBParser()

        assert parser._clean_message("Normal message") == "Normal message"
        assert parser._clean_message("") == ""
        assert parser._clean_message(None) == ""

    def test_convert_timestamp(self):
        """Test timestamp conversion"""
        parser = WhatsAppDBParser()

        # Test valid timestamp (milliseconds)
        result = parser._convert_ts(1704110400000)
        assert isinstance(result, datetime)

        # Test invalid values
        assert parser._convert_ts(None) is None
        assert parser._convert_ts(0) is None
