import streamlit as st

st.set_page_config(page_title="Help & Supported Formats", layout="wide")

st.title("ℹ️ Help & Supported File Types")

st.markdown(
    """
Welcome to the **Forensic Text Classification Tool**!

Below is a quick, overview of what you can drop into the app.

| Input option | Recognised file types | Typical sources |
|--------------|-----------------------|-----------------|
| **Raw text** | `.txt` | Plain chat logs, scraped text, log files |
| **WhatsApp chat exports** | `.csv`, `.xlsx`, `.ods` | “Export chat” from WhatsApp, Cellebrite/XAMN Excel exports |
| **WhatsApp message databases** | `.db`, `.sqlite` | Decrypted `msgstore.db` (Android) or `ChatStorage.sqlite` (iOS) |

### How do I use it?

1. **Raw Text** – paste any German text and hit **Classify Text**.  
2. **Upload File** – drag a supported file and let the tool scan it.  
3. **Folder Path** – point to a folder with many files to get an overview dashboard.

> 💡 *Tip*: Turn on **Context-Aware Scan** when single messages are too short to classify reliably.

### Where does the data go?

Everything is processed **locally**. The tool never sends your evidence to the internet.

### Need more help?

Use the sidebar toggles to turn explanations on/off, or switch back to the main app page from the Streamlit navigation.
    """
)
