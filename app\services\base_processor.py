from pathlib import Path
from typing import Dict
from app.services.whatsapp_processor import WhatsAppProcessor
from app.utils.logger import logger

class BaseProcessor:
    def __init__(self, session_gap_minutes: int = 30, context_window_size: int = 3):
        self.session_gap_minutes = session_gap_minutes
        self.context_window_size = context_window_size

    def detect_and_process(self, input_path: str, context_aware: bool = False) -> Dict:
        path = Path(input_path)
        if not path.exists():
            raise FileNotFoundError(f"Path does not exist: {input_path}")

        if path.is_file() and path.suffix.lower() == ".txt":
            raise ValueError(".txt (raw text) files should not be passed to BaseProcessor")

        logger.info(f"Attempting to process file/folder: {input_path}")

        # Add platform-specific processors here
        try:
            logger.info("Attempting WhatsApp processing...")

            # Log if it's an .xlsx file
            if path.is_file() and path.suffix.lower() == ".xlsx":
                logger.info(f"Detected .xlsx file. Possibly XAMN-style export: {path.name}")

            processor = WhatsAppProcessor(
                session_gap_minutes=self.session_gap_minutes,
                context_window_size=self.context_window_size
            )
            result = processor.process_whatsapp_data(str(path), context_aware=context_aware)
            result["platform"] = "whatsapp"
            return result

        except Exception as e:
            logger.warning(f"WhatsApp processing failed: {e}")

        raise ValueError("Unsupported or unrecognized platform for the given file or folder.")
