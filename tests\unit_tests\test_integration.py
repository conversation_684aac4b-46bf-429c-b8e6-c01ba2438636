"""
Integration tests for the complete processing pipeline
"""
import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
from datetime import datetime, timezone, timedelta
from pathlib import Path
import pandas as pd
import tempfile
import shutil

from app.services.whatsapp_processor import WhatsAppProcessor
from app.services.base_processor import BaseProcessor
from app.platform.whatsapp_parser import WhatsAppParser
from app.services.sessionizer import Sessionizer


class TestWhatsAppProcessingPipeline:
    """Integration tests for WhatsApp processing pipeline"""
    
    @pytest.fixture
    def sample_whatsapp_data(self):
        """Sample WhatsApp data for testing"""
        return [
            {
                "timestamp": datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
                "text": "Hallo, wie geht es dir?",
                "sender": "Alice",
                "chat_id": "test_chat",
                "chat_type": "single",
                "platform": "whatsapp"
            },
            {
                "timestamp": datetime(2024, 1, 1, 10, 5, 0, tzinfo=timezone.utc),
                "text": "Mir geht es gut, danke!",
                "sender": "Bob",
                "chat_id": "test_chat",
                "chat_type": "single",
                "platform": "whatsapp"
            },
            {
                "timestamp": datetime(2024, 1, 1, 10, 30, 0, tzinfo=timezone.utc),  # New session
                "text": "Verkaufst du illegale Substanzen?",
                "sender": "Alice",
                "chat_id": "test_chat",
                "chat_type": "single",
                "platform": "whatsapp"
            }
        ]
    
    @patch('app.services.whatsapp_processor.classifier')
    def test_complete_whatsapp_processing_pipeline(self, mock_classifier, sample_whatsapp_data, temp_dir):
        """Test complete WhatsApp processing from parsing to classification"""
        # Setup mock classifier
        mock_classifier.classify_standard_inputs.return_value = [
            {
                **msg,
                "gbert-base_prediction": "Non-Relevant" if i < 2 else "Relevant",
                "gbert-base_confidence": 0.7 if i < 2 else 0.9
            }
            for i, msg in enumerate(sample_whatsapp_data)
        ]
        
        # Create test XLSX file
        df = pd.DataFrame({
            "zeit": ["2024-01-01 10:00:00", "2024-01-01 10:05:00", "2024-01-01 10:30:00"],
            "nachricht": ["Hallo, wie geht es dir?", "Mir geht es gut, danke!", "Verkaufst du illegale Substanzen?"],
            "von": ["Alice", "Bob", "Alice"],
            "gruppenname": ["test_chat", "test_chat", "test_chat"]
        })
        test_file = temp_dir / "test_whatsapp.xlsx"
        df.to_excel(test_file, index=False)
        
        # Process with WhatsAppProcessor
        processor = WhatsAppProcessor(session_gap_minutes=10, context_window_size=3)
        
        with patch.object(processor.parser, 'detect_platform_files') as mock_detect:
            mock_detect.return_value = {
                "whatsapp_exports": [test_file],
                "whatsapp_databases": []
            }
            
            result = processor.process_whatsapp_data(str(temp_dir))
        
        # Verify results
        assert "results" in result
        assert "stats" in result
        assert isinstance(result["results"], list)
        
        # Should have sessions due to time gap
        if result["results"]:
            # Check if sessionized (has session_id) or flat format
            first_result = result["results"][0]
            if "session_id" in first_result:
                # Sessionized format
                assert len(result["results"]) >= 1  # At least one session
            else:
                # Flat format - should have all messages
                assert len(result["results"]) == 3
    
    def test_sessionizer_integration(self, sample_whatsapp_data):
        """Test sessionizer integration with real data"""
        sessionizer = Sessionizer(session_gap=timedelta(minutes=10))
        
        sessions = sessionizer.sessionize(sample_whatsapp_data)
        
        # Should create 2 sessions due to 25-minute gap between message 2 and 3
        assert len(sessions) == 2
        assert sessions[0]["session_id"] == 1
        assert sessions[1]["session_id"] == 2
        assert len(sessions[0]["messages"]) == 2  # First two messages
        assert len(sessions[1]["messages"]) == 1  # Last message
    
    @patch('app.services.whatsapp_processor.classifier')
    def test_context_aware_processing(self, mock_classifier, sample_whatsapp_data, temp_dir):
        """Test context-aware processing integration"""
        # Setup mock to verify context_aware parameter is passed
        mock_classifier.classify_standard_inputs.return_value = [
            {**msg, "gbert-base_prediction": "Relevant", "gbert-base_confidence": 0.8}
            for msg in sample_whatsapp_data
        ]
        
        # Create test file
        df = pd.DataFrame({
            "zeit": ["2024-01-01 10:00:00"],
            "nachricht": ["Test message"],
            "von": ["User"],
            "gruppenname": ["chat"]
        })
        test_file = temp_dir / "test.xlsx"
        df.to_excel(test_file, index=False)
        
        processor = WhatsAppProcessor(session_gap_minutes=10, context_window_size=3)
        
        with patch.object(processor.parser, 'detect_platform_files') as mock_detect:
            mock_detect.return_value = {
                "whatsapp_exports": [test_file],
                "whatsapp_databases": []
            }
            
            # Test with context_aware=True
            result = processor.process_whatsapp_data(str(temp_dir), context_aware=True)
            
            # Verify classifier was called with context_aware=True
            mock_classifier.classify_standard_inputs.assert_called()
            call_args = mock_classifier.classify_standard_inputs.call_args
            assert call_args[1]["context_aware"] is True


class TestBaseProcessorIntegration:
    """Integration tests for BaseProcessor"""
    
    @patch('app.services.base_processor.WhatsAppProcessor')
    def test_base_processor_whatsapp_detection(self, mock_whatsapp_processor, temp_dir):
        """Test BaseProcessor correctly detects and processes WhatsApp files"""
        # Setup mock
        mock_processor_instance = Mock()
        mock_processor_instance.process_whatsapp_data.return_value = {
            "results": [],
            "stats": {"messages": 0}
        }
        mock_whatsapp_processor.return_value = mock_processor_instance
        
        # Create test XLSX file
        test_file = temp_dir / "test.xlsx"
        test_file.touch()
        
        processor = BaseProcessor(session_gap_minutes=15, context_window_size=4)
        result = processor.detect_and_process(str(test_file))
        
        # Verify WhatsAppProcessor was instantiated with correct parameters
        mock_whatsapp_processor.assert_called_once_with(
            session_gap_minutes=15,
            context_window_size=4
        )
        
        # Verify processing was called
        mock_processor_instance.process_whatsapp_data.assert_called_once_with(
            str(test_file), context_aware=False
        )
        
        # Verify platform was set
        assert result["platform"] == "whatsapp"
    
    def test_base_processor_txt_file_rejection(self, temp_dir):
        """Test BaseProcessor rejects .txt files"""
        txt_file = temp_dir / "test.txt"
        txt_file.write_text("Test content")
        
        processor = BaseProcessor()
        
        with pytest.raises(ValueError, match=".txt \\(raw text\\) files should not be passed"):
            processor.detect_and_process(str(txt_file))


class TestEndToEndProcessing:
    """End-to-end integration tests"""
    
    @patch('app.core.text_classifier.classifier')
    def test_end_to_end_whatsapp_xlsx_processing(self, mock_classifier, temp_dir):
        """Test complete end-to-end processing of WhatsApp XLSX file"""
        # Create realistic test data
        df = pd.DataFrame({
            "zeit": [
                "2024-01-01 10:00:00",
                "2024-01-01 10:05:00", 
                "2024-01-01 10:07:00",
                "2024-01-01 10:20:00"  # Session break
            ],
            "nachricht": [
                "Hallo, wie geht es dir?",
                "Gut, danke! Und dir?",
                "Auch gut, danke!",
                "Hast du Interesse an illegalen Substanzen?"
            ],
            "von": ["Alice", "Bob", "Alice", "Bob"],
            "gruppenname": ["TestGroup", "TestGroup", "TestGroup", "TestGroup"]
        })
        
        test_file = temp_dir / "whatsapp_export.xlsx"
        df.to_excel(test_file, index=False)
        
        # Setup mock classifier to return realistic predictions
        def mock_classify_side_effect(messages, context_aware=False):
            results = []
            for msg in messages:
                # Mark the last message as relevant (about illegal substances)
                is_relevant = "illegal" in msg.get("text", "").lower()
                results.append({
                    **msg,
                    "gbert-base_prediction": "Relevant" if is_relevant else "Non-Relevant",
                    "gbert-base_confidence": 0.92 if is_relevant else 0.78,
                    "gbert-large_prediction": "Relevant" if is_relevant else "Non-Relevant", 
                    "gbert-large_confidence": 0.89 if is_relevant else 0.82
                })
            return results
        
        mock_classifier.classify_standard_inputs.side_effect = mock_classify_side_effect
        
        # Process through BaseProcessor (simulating API call)
        processor = BaseProcessor(session_gap_minutes=10, context_window_size=3)
        result = processor.detect_and_process(str(test_file), context_aware=True)
        
        # Verify results structure
        assert "results" in result
        assert "stats" in result
        assert "platform" in result
        assert result["platform"] == "whatsapp"
        
        # Verify stats
        stats = result["stats"]
        assert "messages" in stats
        assert stats["messages"] == 4
        
        # Verify classification results
        results = result["results"]
        assert len(results) > 0
        
        # Check if results are sessionized or flat
        if isinstance(results[0], dict) and "session_id" in results[0]:
            # Sessionized format
            total_messages = sum(len(session["messages"]) for session in results)
            assert total_messages == 4
            
            # Find the relevant message
            relevant_found = False
            for session in results:
                for msg in session["messages"]:
                    if any(msg.get(f"{model}_prediction") == "Relevant" 
                          for model in ["gbert-base", "gbert-large"]):
                        relevant_found = True
                        assert "illegal" in msg["text"].lower()
            assert relevant_found
        else:
            # Flat format
            assert len(results) == 4
            relevant_messages = [
                msg for msg in results 
                if any(msg.get(f"{model}_prediction") == "Relevant" 
                      for model in ["gbert-base", "gbert-large"])
            ]
            assert len(relevant_messages) == 1
            assert "illegal" in relevant_messages[0]["text"].lower()
    
    def test_error_propagation_through_pipeline(self, temp_dir):
        """Test error handling propagates correctly through the pipeline"""
        # Create invalid file
        invalid_file = temp_dir / "invalid.xlsx"
        invalid_file.write_text("This is not a valid Excel file")
        
        processor = BaseProcessor()
        
        # Should raise ValueError about unsupported platform
        with pytest.raises(ValueError, match="Unsupported or unrecognized platform"):
            processor.detect_and_process(str(invalid_file))
    
    @patch('app.services.whatsapp_processor.classifier')
    def test_empty_file_handling(self, mock_classifier, temp_dir):
        """Test handling of empty or invalid WhatsApp files"""
        # Create empty XLSX file
        df = pd.DataFrame()
        empty_file = temp_dir / "empty.xlsx"
        df.to_excel(empty_file, index=False)
        
        mock_classifier.classify_standard_inputs.return_value = []
        
        processor = BaseProcessor()
        
        # Should handle empty files gracefully
        with pytest.raises(ValueError, match="Unsupported or unrecognized platform"):
            processor.detect_and_process(str(empty_file))
