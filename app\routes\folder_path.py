# app/routes/folder_path.py

from fastapi import APIRouter, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pathlib import Path

from app.core.text_classifier import classifier
from app.services.base_processor import BaseProcessor
from app.services.analysis_service import analysis_service # <-- IMPORT THE NEW SERVICE
from app.utils.logger import logger
from app.config import DEFAULT_SESSION_GAP, DEFAULT_CONTEXT_WINDOW

router = APIRouter()

@router.post("/classify-folder")
async def classify_folder_api(
    folder_path: str = Form(...),
    session_gap_minutes: int = Form(DEFAULT_SESSION_GAP),
    context_window_size: int = Form(DEFAULT_CONTEXT_WINDOW),
    context_aware: bool = Form(False),
):
    """
    Handles the analysis of all files in a folder. For each file, it performs
    classification and then RAG analysis, returning a summarized report per file.
    """
    folder = Path(folder_path)
    if not folder.is_dir():
        raise HTTPException(status_code=400, detail="Invalid folder path provided.")

    logger.info(f"Starting RAG analysis for folder: {folder.resolve()}")
    
    analysis_outputs = {}
    errors = []

    for file_path in folder.rglob("*"):
        if not file_path.is_file():
            continue
        
        relative_path = str(file_path.relative_to(folder))
        logger.info(f"Analyzing file: {relative_path}")
        
        try:
            # --- Step 1: Classification (same as file_upload) ---
            all_messages = []
            if file_path.suffix.lower() == ".txt":
                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                    content = f.read()
                all_messages = classifier.classify_text(content, context_aware=context_aware)
            elif file_path.suffix.lower() in [".xlsx", ".csv", ".ods", ".db", ".sqlite"]:
                processor = BaseProcessor(session_gap_minutes, context_window_size)
                result = processor.detect_and_process(str(file_path), context_aware)
                for session in result.get("results", []):
                    all_messages.extend(session.get("messages", []))
            else:
                continue # Skip unsupported file types

            # --- Step 2: RAG Analysis for this file ---
            if not all_messages:
                analysis_report = {"status": "completed", "summary": "No messages found in file.", "clusters": []}
            else:
                analysis_report = analysis_service.cluster_and_summarize(all_messages)
            
            analysis_outputs[relative_path] = analysis_report

        except Exception as e:
            logger.error(f"Failed to analyze file {relative_path}: {e}", exc_info=True)
            errors.append(f"{relative_path}: {str(e)}")

    logger.info(f"Folder analysis complete for: {folder.resolve()}")
    return JSONResponse(jsonable_encoder({
        "status": "completed",
        "analysis_outputs": analysis_outputs,
        "errors": errors,
    }))
