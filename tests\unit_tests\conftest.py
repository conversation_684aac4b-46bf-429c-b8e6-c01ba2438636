"""
Pytest configuration and fixtures for unit tests
"""
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, MagicMock
import pandas as pd
from datetime import datetime, timezone
import sqlite3
import os
import sys

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)

@pytest.fixture
def sample_messages():
    """Sample message data for testing"""
    return [
        {
            "timestamp": datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            "text": "Das ist eine relevante Nachricht über Drogen",
            "sender": "user1",
            "chat_id": "test_chat",
            "chat_type": "single",
            "platform": "whatsapp"
        },
        {
            "timestamp": datetime(2024, 1, 1, 10, 5, 0, tzinfo=timezone.utc),
            "text": "Hallo, wie geht es dir?",
            "sender": "user2", 
            "chat_id": "test_chat",
            "chat_type": "single",
            "platform": "whatsapp"
        },
        {
            "timestamp": datetime(2024, 1, 1, 10, 10, 0, tzinfo=timezone.utc),
            "text": "Ich verkaufe illegale Substanzen",
            "sender": "user1",
            "chat_id": "test_chat", 
            "chat_type": "single",
            "platform": "whatsapp"
        }
    ]

@pytest.fixture
def sample_whatsapp_xlsx(temp_dir):
    """Create a sample WhatsApp XLSX file"""
    data = {
        "zeit": ["2024-01-01 10:00:00", "2024-01-01 10:05:00", "2024-01-01 10:10:00"],
        "nachricht": ["Test message 1", "Test message 2", "Test message 3"],
        "von": ["User1", "User2", "User1"],
        "gruppenname": ["TestGroup", "TestGroup", "TestGroup"]
    }
    df = pd.DataFrame(data)
    file_path = temp_dir / "test_whatsapp.xlsx"
    df.to_excel(file_path, index=False)
    return file_path

@pytest.fixture
def sample_whatsapp_db(temp_dir):
    """Create a sample WhatsApp database file"""
    db_path = temp_dir / "test_msgstore.db"
    conn = sqlite3.connect(db_path)
    
    # Create tables
    conn.execute("""
        CREATE TABLE message (
            _id INTEGER PRIMARY KEY,
            chat_row_id INTEGER,
            sender_jid_row_id INTEGER,
            from_me INTEGER,
            text_data TEXT,
            timestamp INTEGER
        )
    """)
    
    conn.execute("""
        CREATE TABLE chat (
            _id INTEGER PRIMARY KEY,
            jid_row_id INTEGER,
            subject TEXT
        )
    """)
    
    conn.execute("""
        CREATE TABLE jid (
            _id INTEGER PRIMARY KEY,
            raw_string TEXT,
            user TEXT
        )
    """)
    
    # Insert sample data
    conn.execute("INSERT INTO jid VALUES (1, '<EMAIL>', 'user1')")
    conn.execute("INSERT INTO jid VALUES (2, '<EMAIL>', 'user2')")
    conn.execute("INSERT INTO jid VALUES (3, '<EMAIL>', 'testgroup')")
    
    conn.execute("INSERT INTO chat VALUES (1, 3, 'Test Group')")
    
    conn.execute("INSERT INTO message VALUES (1, 1, 1, 0, 'Test message 1', 1704110400000)")
    conn.execute("INSERT INTO message VALUES (2, 1, 2, 0, 'Test message 2', 1704110700000)")
    conn.execute("INSERT INTO message VALUES (3, 1, 1, 0, 'Test message 3', 1704111000000)")
    
    conn.commit()
    conn.close()
    return db_path

@pytest.fixture
def mock_text_classifier():
    """Mock TextClassifier for testing"""
    mock_classifier = Mock()
    mock_classifier.classify_text.return_value = [
        {
            "text": "test message",
            "line_number": 1,
            "prediction": "Relevant",
            "confidence": 0.85
        }
    ]
    mock_classifier.classify_standard_inputs.return_value = [
        {
            "text": "test message",
            "timestamp": datetime.now(timezone.utc),
            "prediction": "Relevant", 
            "confidence": 0.85
        }
    ]
    return mock_classifier

@pytest.fixture
def mock_analysis_service():
    """Mock AnalysisService for testing"""
    mock_service = Mock()
    mock_service.cluster_and_summarize.return_value = {
        "status": "completed",
        "summary": "Test analysis complete",
        "clusters": [
            {
                "cluster_id": "Thema 1",
                "summary": "Test cluster summary",
                "message_count": 2,
                "messages": []
            }
        ]
    }
    mock_service.health_check.return_value = {
        "status": "healthy",
        "model_file_exists": True,
        "models_loaded": True
    }
    return mock_service

@pytest.fixture
def sample_text_file(temp_dir):
    """Create a sample text file"""
    file_path = temp_dir / "test.txt"
    content = "Line 1: Test message\nLine 2: Another test message\nLine 3: Final test message"
    file_path.write_text(content, encoding='utf-8')
    return file_path
